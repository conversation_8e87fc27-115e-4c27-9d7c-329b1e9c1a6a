import { relations } from "drizzle-orm/relations";
import { user, authenticator, session, account, publishers, publisherToReportType, reportType } from "./schema";

export const authenticatorRelations = relations(authenticator, ({one}) => ({
	user: one(user, {
		fields: [authenticator.userId],
		references: [user.id]
	}),
}));

export const userRelations = relations(user, ({many}) => ({
	authenticators: many(authenticator),
	sessions: many(session),
	accounts: many(account),
}));

export const sessionRelations = relations(session, ({one}) => ({
	user: one(user, {
		fields: [session.userId],
		references: [user.id]
	}),
}));

export const accountRelations = relations(account, ({one}) => ({
	user: one(user, {
		fields: [account.userId],
		references: [user.id]
	}),
}));

export const publisherToReportTypeRelations = relations(publisherToReportType, ({one}) => ({
	publisher: one(publishers, {
		fields: [publisherToReportType.publisherId],
		references: [publishers.id]
	}),
	reportType: one(reportType, {
		fields: [publisherToReportType.reportTypeId],
		references: [reportType.id]
	}),
}));

export const publishersRelations = relations(publishers, ({many}) => ({
	publisherToReportTypes: many(publisherToReportType),
}));

export const reportTypeRelations = relations(reportType, ({many}) => ({
	publisherToReportTypes: many(publisherToReportType),
}));