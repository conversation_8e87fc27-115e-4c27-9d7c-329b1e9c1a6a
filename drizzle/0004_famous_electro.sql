CREATE TABLE "reps" (
	"id" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "reps_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"name" text NOT NULL,
	"email" text NOT NULL,
	"createdAt" timestamp DEFAULT '2025-06-05 19:28:34.008' NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "period_reports" ADD COLUMN "createdAt" timestamp DEFAULT '2025-06-05 19:28:34.008' NOT NULL;--> statement-breakpoint
ALTER TABLE "period_reports" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "publishers" ADD COLUMN "createdAt" timestamp DEFAULT '2025-06-05 19:28:34.007' NOT NULL;--> statement-breakpoint
ALTER TABLE "publishers" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "publisher_to_report_type" ADD COLUMN "createdAt" timestamp DEFAULT '2025-06-05 19:28:34.008' NOT NULL;--> statement-breakpoint
ALTER TABLE "publisher_to_report_type" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "report_type" ADD COLUMN "createdAt" timestamp DEFAULT '2025-06-05 19:28:34.008' NOT NULL;--> statement-breakpoint
ALTER TABLE "report_type" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "store_aliases" ADD COLUMN "createdAt" timestamp DEFAULT '2025-06-05 19:28:34.007' NOT NULL;--> statement-breakpoint
ALTER TABLE "store_aliases" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "store_types" ADD COLUMN "createdAt" timestamp DEFAULT '2025-06-05 19:28:34.005' NOT NULL;--> statement-breakpoint
ALTER TABLE "store_types" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "stores" ADD COLUMN "repId" integer;--> statement-breakpoint
ALTER TABLE "stores" ADD COLUMN "createdAt" timestamp DEFAULT '2025-06-05 19:28:34.006' NOT NULL;--> statement-breakpoint
ALTER TABLE "stores" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;