import { pgTable, foreignKey, unique, text, integer, boolean, timestamp, numeric, index, primaryKey } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"



export const authenticator = pgTable("authenticator", {
	credentialId: text().notNull(),
	userId: text().notNull(),
	providerAccountId: text().notNull(),
	credentialPublicKey: text().notNull(),
	counter: integer().notNull(),
	credentialDeviceType: text().notNull(),
	credentialBackedUp: boolean().notNull(),
	transports: text(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "authenticator_userId_user_id_fk"
		}).onDelete("cascade"),
	unique("authenticator_credentialID_unique").on(table.credentialId),
]);

export const publishers = pgTable("publishers", {
	id: integer().primaryKey().generatedByDefaultAsIdentity({ name: "publishers_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	name: text().notNull(),
});

export const periodReports = pgTable("period_reports", {
	id: integer().primaryKey().generatedByDefaultAsIdentity({ name: "period_reports_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	storeId: integer().notNull(),
	publisherId: integer().notNull(),
	periodStart: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
	periodEnd: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
	netRevenue: numeric(),
	netQuantity: integer(),
	grossRevenue: numeric(),
	grossQuantity: integer(),
	returnRevenue: numeric(),
	returnQuantity: integer(),
});

export const reportType = pgTable("report_type", {
	id: integer().primaryKey().generatedByDefaultAsIdentity({ name: "report_type_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	name: text().notNull(),
	dateNeeded: boolean().notNull(),
});

export const session = pgTable("session", {
	sessionToken: text().primaryKey().notNull(),
	userId: text().notNull(),
	expires: timestamp({ mode: 'string' }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "session_userId_user_id_fk"
		}).onDelete("cascade"),
]);

export const storeAliases = pgTable("store_aliases", {
	id: integer().primaryKey().generatedByDefaultAsIdentity({ name: "store_aliases_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	publisherId: integer().notNull(),
	storeId: integer().notNull(),
	publisherStoreId: text().notNull(),
	identifier: text().notNull(),
	identifier2: text(),
});

export const stores = pgTable("stores", {
	id: integer().primaryKey().generatedByDefaultAsIdentity({ name: "stores_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	name: text().notNull(),
	branchName: text(),
}, (table) => [
	index("name_idx").using("btree", table.name.asc().nullsLast().op("text_ops")),
]);

export const user = pgTable("user", {
	id: text().primaryKey().notNull(),
	name: text(),
	email: text(),
	emailVerified: timestamp({ mode: 'string' }),
	image: text(),
}, (table) => [
	unique("user_email_unique").on(table.email),
]);

export const verificationToken = pgTable("verificationToken", {
	identifier: text().notNull(),
	token: text().notNull(),
	expires: timestamp({ mode: 'string' }).notNull(),
});

export const account = pgTable("account", {
	userId: text().notNull(),
	type: text().notNull(),
	provider: text().notNull(),
	providerAccountId: text().notNull(),
	refreshToken: text("refresh_token"),
	accessToken: text("access_token"),
	expiresAt: integer("expires_at"),
	tokenType: text("token_type"),
	scope: text(),
	idToken: text("id_token"),
	sessionState: text("session_state"),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "account_userId_user_id_fk"
		}).onDelete("cascade"),
]);

export const publisherToReportType = pgTable("publisher_to_report_type", {
	publisherId: integer().notNull(),
	reportTypeId: integer().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.publisherId],
			foreignColumns: [publishers.id],
			name: "publisher_to_report_type_publisherId_publishers_id_fk"
		}),
	foreignKey({
			columns: [table.reportTypeId],
			foreignColumns: [reportType.id],
			name: "publisher_to_report_type_reportTypeId_report_type_id_fk"
		}),
	primaryKey({ columns: [table.publisherId, table.reportTypeId], name: "publisher_to_report_type_publisherId_reportTypeId_pk"}),
]);
