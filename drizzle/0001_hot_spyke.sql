CREATE TABLE "store_types" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "store_types_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"type" text NOT NULL
);
--> statement-breakpoint
ALTER TABLE "stores" ADD COLUMN "address" text;--> statement-breakpoint
ALTER TABLE "stores" ADD COLUMN "contactName" text;--> statement-breakpoint
ALTER TABLE "stores" ADD COLUMN "contactInfo" text;--> statement-breakpoint
ALTER TABLE "stores" ADD COLUMN "typeId" integer;