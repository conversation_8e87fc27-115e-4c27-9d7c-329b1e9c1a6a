{"id": "********-3768-4e67-9287-b37164f15ee4", "prevId": "53c8cd45-74f4-41f5-b769-5a4e685aeb0b", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"account_userId_user_id_fk": {"name": "account_userId_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.authenticator": {"name": "authenticator", "schema": "", "columns": {"credentialID": {"name": "credentialID", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true}, "credentialPublicKey": {"name": "credentialPublicKey", "type": "text", "primaryKey": false, "notNull": true}, "counter": {"name": "counter", "type": "integer", "primaryKey": false, "notNull": true}, "credentialDeviceType": {"name": "credentialDeviceType", "type": "text", "primaryKey": false, "notNull": true}, "credentialBackedUp": {"name": "credentialBackedUp", "type": "boolean", "primaryKey": false, "notNull": true}, "transports": {"name": "transports", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"authenticator_userId_user_id_fk": {"name": "authenticator_userId_user_id_fk", "tableFrom": "authenticator", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"authenticator_credentialID_unique": {"name": "authenticator_credentialID_unique", "nullsNotDistinct": false, "columns": ["credentialID"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.period_reports": {"name": "period_reports", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "period_reports_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "storeId": {"name": "storeId", "type": "integer", "primaryKey": false, "notNull": true}, "publisherId": {"name": "publisherId", "type": "integer", "primaryKey": false, "notNull": true}, "periodStart": {"name": "periodStart", "type": "date", "primaryKey": false, "notNull": true}, "periodEnd": {"name": "periodEnd", "type": "date", "primaryKey": false, "notNull": true}, "netRevenue": {"name": "netRevenue", "type": "numeric", "primaryKey": false, "notNull": false}, "netQuantity": {"name": "netQuantity", "type": "integer", "primaryKey": false, "notNull": false}, "grossRevenue": {"name": "grossRevenue", "type": "numeric", "primaryKey": false, "notNull": false}, "grossQuantity": {"name": "grossQuantity", "type": "integer", "primaryKey": false, "notNull": false}, "returnRevenue": {"name": "returnRevenue", "type": "numeric", "primaryKey": false, "notNull": false}, "returnQuantity": {"name": "returnQuantity", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.publishers": {"name": "publishers", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "publishers_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.publisher_to_report_type": {"name": "publisher_to_report_type", "schema": "", "columns": {"publisherId": {"name": "publisherId", "type": "integer", "primaryKey": false, "notNull": true}, "reportTypeId": {"name": "reportTypeId", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"publisher_to_report_type_publisherId_publishers_id_fk": {"name": "publisher_to_report_type_publisherId_publishers_id_fk", "tableFrom": "publisher_to_report_type", "tableTo": "publishers", "columnsFrom": ["publisherId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "publisher_to_report_type_reportTypeId_report_type_id_fk": {"name": "publisher_to_report_type_reportTypeId_report_type_id_fk", "tableFrom": "publisher_to_report_type", "tableTo": "report_type", "columnsFrom": ["reportTypeId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"publisher_to_report_type_publisherId_reportTypeId_pk": {"name": "publisher_to_report_type_publisherId_reportTypeId_pk", "columns": ["publisherId", "reportTypeId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.report_type": {"name": "report_type", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "report_type_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "dateNeeded": {"name": "dateNeeded", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_userId_user_id_fk": {"name": "session_userId_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.store_aliases": {"name": "store_aliases", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "store_aliases_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "publisherId": {"name": "publisherId", "type": "integer", "primaryKey": false, "notNull": true}, "storeId": {"name": "storeId", "type": "integer", "primaryKey": false, "notNull": true}, "publisherStoreId": {"name": "publisherStoreId", "type": "text", "primaryKey": false, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "identifier2": {"name": "identifier2", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.store_types": {"name": "store_types", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "store_types_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stores": {"name": "stores", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "stores_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "branchName": {"name": "branchName", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "contactName": {"name": "contactName", "type": "text", "primaryKey": false, "notNull": false}, "contactInfo": {"name": "contactInfo", "type": "text", "primaryKey": false, "notNull": false}, "typeId": {"name": "typeId", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"name_idx": {"name": "name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verificationToken": {"name": "verificationToken", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}