import { relations } from "drizzle-orm";
import {
  boolean,
  date,
  index,
  integer,
  numeric,
  pgTable,
  primaryKey,
  text,
  timestamp,
  unique,
} from "drizzle-orm/pg-core";
import { type AdapterAccountType } from "next-auth/adapters";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod/v4";

export const storeTypes = pgTable("store_types", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  name: text().notNull(),
  createdAt: timestamp().notNull().defaultNow(),
  updatedAt: timestamp()
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const storeTypesRelations = relations(storeTypes, ({ many }) => ({
  stores: many(stores),
}));

export const stores = pgTable(
  "stores",
  {
    id: integer().primaryKey().generatedByDefaultAsIdentity(),
    name: text().notNull(),
    branchName: text(),
    contactName: text(),
    contactEmail: text(),
    contactPhone: text(),
    typeId: integer(),
    repId: integer(),
    active: boolean().notNull().default(true),
    streetNumber: text(),
    route: text(),
    subpremise: text(),
    city: text(),
    province: text(),
    country: text(),
    postalCode: text(),
    latitude: numeric(),
    longitude: numeric(),
    createdAt: timestamp().notNull().defaultNow(),
    updatedAt: timestamp()
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (stores) => [index("name_idx").on(stores.name)],
);

export const storesInsertValidator = createInsertSchema(stores, {
  branchName: z.string().trim().optional(),
  contactName: z.string().trim().optional(),
  contactEmail: z.union([z.email(), z.literal("")]),
  contactPhone: z.string().trim().optional(),
  typeId: z.number().optional(),
  repId: z.number().optional(),
  streetNumber: z.string().trim().optional(),
  route: z.string().trim().optional(),
  subpremise: z.string().trim().optional(),
  city: z.string().trim().optional(),
  province: z.string().trim().optional(),
  country: z.string().trim().optional(),
  postalCode: z.string().trim().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
});
export type storesInsert = z.infer<typeof storesInsertValidator>;

export const storesRelations = relations(stores, ({ many, one }) => ({
  aliases: many(storeAliases),
  periodReports: many(periodReports),
  type: one(storeTypes, {
    fields: [stores.typeId],
    references: [storeTypes.id],
  }),
  rep: one(reps, {
    fields: [stores.repId],
    references: [reps.id],
  }),
}));

export const publishers = pgTable("publishers", {
  id: integer().primaryKey().generatedByDefaultAsIdentity(),
  name: text().notNull(),
  createdAt: timestamp().notNull().defaultNow(),
  updatedAt: timestamp()
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const publishersRelations = relations(publishers, ({ many }) => ({
  periodReports: many(periodReports),
  publishersToReportTypes: many(publishersToReportTypes),
  storeAliases: many(storeAliases),
}));

export const storeAliases = pgTable("store_aliases", {
  id: integer().primaryKey().generatedByDefaultAsIdentity(),
  publisherId: integer().notNull(),
  storeId: integer().notNull(),
  publisherStoreId: text().notNull(),
  identifier: text().notNull(),
  identifier2: text(),
  createdAt: timestamp().notNull().defaultNow(),
  updatedAt: timestamp()
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const aliasRelations = relations(storeAliases, ({ one }) => ({
  store: one(stores, {
    fields: [storeAliases.storeId],
    references: [stores.id],
  }),
  publisher: one(publishers, {
    fields: [storeAliases.publisherId],
    references: [publishers.id],
  }),
}));

export const periodReports = pgTable(
  "period_reports",
  {
    id: integer().primaryKey().generatedByDefaultAsIdentity(),
    storeId: integer().notNull(),
    publisherId: integer().notNull(),
    periodStart: date().notNull(),
    periodEnd: date().notNull(),
    netRevenue: numeric(),
    netQuantity: integer(),
    grossRevenue: numeric(),
    grossQuantity: integer(),
    returnRevenue: numeric(),
    returnQuantity: integer(),
    createdAt: timestamp().notNull().defaultNow(),
    updatedAt: timestamp()
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (t) => [
    unique("unique_report").on(
      t.storeId,
      t.publisherId,
      t.periodStart,
      t.periodEnd,
    ),
  ],
);

export const periodReportsRelations = relations(periodReports, ({ one }) => ({
  store: one(stores, {
    fields: [periodReports.storeId],
    references: [stores.id],
  }),
  publisher: one(publishers, {
    fields: [periodReports.publisherId],
    references: [publishers.id],
  }),
}));

export const reportTypes = pgTable("report_type", {
  id: integer().primaryKey().generatedByDefaultAsIdentity(),
  name: text().notNull(),
  dateNeeded: boolean().notNull(),
  createdAt: timestamp().notNull().defaultNow(),
  updatedAt: timestamp()
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const reportTypesRelations = relations(reportTypes, ({ many }) => ({
  publishersToReportTypes: many(publishersToReportTypes),
}));

export const publishersToReportTypes = pgTable(
  "publisher_to_report_type",
  {
    publisherId: integer()
      .notNull()
      .references(() => publishers.id),
    reportTypeId: integer()
      .notNull()
      .references(() => reportTypes.id),
    createdAt: timestamp().notNull().defaultNow(),
    updatedAt: timestamp()
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (t) => [primaryKey({ columns: [t.publisherId, t.reportTypeId] })],
);

export const publishersToReportTypesRelations = relations(
  publishersToReportTypes,
  ({ one }) => ({
    publisher: one(publishers, {
      fields: [publishersToReportTypes.publisherId],
      references: [publishers.id],
    }),
    reportType: one(reportTypes, {
      fields: [publishersToReportTypes.reportTypeId],
      references: [reportTypes.id],
    }),
  }),
);

export const reps = pgTable("reps", {
  id: integer().primaryKey().generatedByDefaultAsIdentity(),
  name: text().notNull(),
  email: text().notNull(),
  createdAt: timestamp().notNull().defaultNow(),
  updatedAt: timestamp()
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const repsRelations = relations(reps, ({ many }) => ({
  stores: many(stores),
}));

/**
 * Auth.js schema below here
 */

export const users = pgTable("user", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  name: text("name"),
  email: text("email").unique(),
  emailVerified: timestamp("emailVerified", { mode: "date" }),
  image: text("image"),
});

export const accounts = pgTable(
  "account",
  {
    userId: text("userId")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    type: text("type").$type<AdapterAccountType>().notNull(),
    provider: text("provider").notNull(),
    providerAccountId: text("providerAccountId").notNull(),
    refresh_token: text("refresh_token"),
    access_token: text("access_token"),
    expires_at: integer("expires_at"),
    token_type: text("token_type"),
    scope: text("scope"),
    id_token: text("id_token"),
    session_state: text("session_state"),
  },
  (account) => [
    {
      compoundKey: primaryKey({
        columns: [account.provider, account.providerAccountId],
      }),
    },
  ],
);

export const sessions = pgTable("session", {
  sessionToken: text("sessionToken").primaryKey(),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  expires: timestamp("expires", { mode: "date" }).notNull(),
});

export const verificationTokens = pgTable(
  "verificationToken",
  {
    identifier: text("identifier").notNull(),
    token: text("token").notNull(),
    expires: timestamp("expires", { mode: "date" }).notNull(),
  },
  (verificationToken) => [
    {
      compositePk: primaryKey({
        columns: [verificationToken.identifier, verificationToken.token],
      }),
    },
  ],
);

export const authenticators = pgTable(
  "authenticator",
  {
    credentialID: text("credentialID").notNull().unique(),
    userId: text("userId")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    providerAccountId: text("providerAccountId").notNull(),
    credentialPublicKey: text("credentialPublicKey").notNull(),
    counter: integer("counter").notNull(),
    credentialDeviceType: text("credentialDeviceType").notNull(),
    credentialBackedUp: boolean("credentialBackedUp").notNull(),
    transports: text("transports"),
  },
  (authenticator) => [
    {
      compositePK: primaryKey({
        columns: [authenticator.userId, authenticator.credentialID],
      }),
    },
  ],
);
