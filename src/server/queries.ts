import "server-only";

import { db } from "./db";
import { and, DrizzleQueryError, eq, inArray } from "drizzle-orm";
import { periodReports, publishers, storeAliases, stores } from "~/server/db/schema";

export const getAllPublishers = async () => {
  const data = await db.query.publishers.findMany({
    with: {
      publishersToReportTypes: {
        with: {
          reportType: true,
        },
      },
    },
  });
  return data.map((publisher) => {
    return {
      id: publisher.id,
      name: publisher.name,
      reportTypes: publisher.publishersToReportTypes.map((pt) => pt.reportType),
    };
  });
};

export type getAllPublishers = Awaited<ReturnType<typeof getAllPublishers>>;

export const getPublisher = async (id: number) => {
  const data = await db.query.publishers.findFirst({
    where: eq(publishers.id, id),
    with: {
      publishersToReportTypes: {
        with: {
          reportType: true,
        },
      },
    },
  });
  if (!data) return undefined;
  return {
    id: data.id,
    name: data.name,
    reportTypes: data.publishersToReportTypes.map((pt) => pt.reportType),
  };
};

export type getPublisher = Awaited<ReturnType<typeof getPublisher>>;

export const getAliasesFromIdsAndPub = async (
  publisherStoreIds: string[],
  publisher: number | undefined,
) => {
  if (!publisher) return [];
  return db.query.storeAliases.findMany({
    where: and(
      inArray(storeAliases.publisherStoreId, publisherStoreIds),
      eq(storeAliases.publisherId, publisher),
    ),
  });
};

export type getAliasesFromIdsAndPub = Awaited<
  ReturnType<typeof getAliasesFromIdsAndPub>
>;

export const getAllReadStores = async () => {
  let data = [];
  try {
    data = await db.query.stores.findMany({
      with: {
        type: true,
      },
    });
  } catch (e) {
    if (e instanceof DrizzleQueryError) {
      console.error(e.message);
      return [];
    }
    throw e;
  }
  return data.map((store) => {
    return {
      ...store,
      type: store.type?.name ?? null,
      formattedAddress: store.streetNumber
        ? `${store.streetNumber} ${store.route} ${store.subpremise}, ${store.city}, ${store.province}, ${store.country} ${store.postalCode}`
        : null,
    };
  });
};

export type getAllReadStores = Awaited<ReturnType<typeof getAllReadStores>>;

export const insertReadStore = async (data: typeof stores.$inferInsert) => {
  return db
    .insert(stores)
    .values(data)
    .returning({ insertedId: stores.id })
    .execute();
};

export const getStoreTypes = async () => {
  return db.query.storeTypes.findMany();
};

export type getStoreTypes = Awaited<ReturnType<typeof getStoreTypes>>;

export const insertPeriodReports = async (
  data: (typeof periodReports.$inferInsert)[],
) => {
  await db.insert(periodReports).values(data).onConflictDoNothing().execute();
};

export const insertNewAlias = async (
  data: typeof storeAliases.$inferInsert,
) => {
  await db.insert(storeAliases).values(data).execute();
};

export type insertNewAlias = Parameters<typeof insertNewAlias>[0];
