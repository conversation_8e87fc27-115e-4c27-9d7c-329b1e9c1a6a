import { Dr<PERSON><PERSON>Adapter } from "@auth/drizzle-adapter";
import { type DefaultSession, type NextAuthConfig } from "next-auth";
import Resend from "@auth/core/providers/resend";

import { env } from "~/env";
import { db } from "~/server/db";
import {
  accounts,
  sessions,
  users,
  verificationTokens,
} from "~/server/db/schema";

declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      // ...other properties
      // role: UserRole;
    } & DefaultSession["user"];
  }

  // interface User {
  //   // ...other properties
  //   // role: UserRole;
  // }
}

export const authConfig = {
  providers: [
    Resend({
      apiKey: env.AUTH_RESEND_KEY,
      from: env.EMAIL_FROM,
    }),
  ],
  adapter: DrizzleAdapter(db, {
    usersTable: users,
    accountsTable: accounts,
    sessionsTable: sessions,
    verificationTokensTable: verificationTokens,
  }),
  callbacks: {
    session: ({ session, user }) => ({
      ...session,
      user: {
        ...session.user,
        id: user.id,
      },
    }),
    async signIn({ user, email }) {
      if (email?.verificationRequest && user.email) {
        console.log("email verification request", user.email);
        const emailDomain = user.email.split("@")[1];
        if (
          (emailDomain && env.ALLOWED_DOMAINS?.includes(emailDomain)) ||
          env.ALLOWED_EMAILS?.includes(user.email)
        ) {
          console.log("allowed verification");
          return true;
        } else {
          console.log("denied verification");
          return "/register";
        }
      } else {
        console.log("login", user.email);
        return true;
      }
    },
  },
} satisfies NextAuthConfig;
