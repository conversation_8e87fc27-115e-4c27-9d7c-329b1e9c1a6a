import { createCallerFactory, createTRPCRouter } from "~/server/api/trpc";
import { publisherRouter } from "~/server/api/routers/publisher";
import { storeRouter } from "~/server/api/routers/stores";
import { salesRouter } from "~/server/api/routers/sales";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  publisher: publisherRouter,
  store: storeRouter,
  sales: salesRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
