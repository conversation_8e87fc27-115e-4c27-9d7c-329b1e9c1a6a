import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import { z } from "zod/v4";
import {
  periodReports,
  type publishers as publishersTable,
  type stores as storesTable,
} from "~/server/db/schema";
import { and, gte, inArray, lte } from "drizzle-orm";
import { type DateRange } from "react-day-picker";
import { db } from "~/server/db";
import { endOfMonth, startOfMonth } from "date-fns";

const getReportsByPubStoreDate = async <
  T extends "store" | "publisher" | undefined = undefined,
>(
  stores: string | string[] | undefined,
  publishers: string | string[] | undefined,
  dateRange: DateRange,
  include?: T,
): Promise<
  T extends "store"
    ? Array<
        typeof periodReports.$inferSelect & {
          store: typeof storesTable.$inferSelect;
        }
      >
    : T extends "publisher"
      ? Array<
          typeof periodReports.$inferSelect & {
            publisher: typeof publishersTable.$inferSelect;
          }
        >
      : Array<typeof periodReports.$inferSelect>
> => {
  const whereConditions = [];

  if (stores) {
    const storeIds = Array.isArray(stores)
      ? stores.map(Number)
      : [Number(stores)];
    if (storeIds.length > 0 && !storeIds.some(isNaN)) {
      whereConditions.push(inArray(periodReports.storeId, storeIds));
    }
  }

  if (publishers) {
    const publisherIds = Array.isArray(publishers)
      ? publishers.map(Number)
      : [Number(publishers)];
    if (publisherIds.length > 0 && !publisherIds.some(isNaN)) {
      whereConditions.push(inArray(periodReports.publisherId, publisherIds));
    }
  }

  if (dateRange && (dateRange.from || dateRange.to)) {
    const dateConditions = [];
    if (dateRange.from) {
      dateConditions.push(
        gte(
          periodReports.periodStart,
          startOfMonth(dateRange.from).toISOString().split("T")[0]!,
        ),
      );
    }
    if (dateRange.to) {
      dateConditions.push(
        lte(
          periodReports.periodEnd,
          endOfMonth(dateRange.to).toISOString().split("T")[0]!,
        ),
      );
    }
    whereConditions.push(and(...dateConditions));
  }

  const finalWhereClause =
    whereConditions.length > 0 ? and(...whereConditions) : undefined;

  return db.query.periodReports.findMany({
    where: finalWhereClause,
    with: include ? { [include]: true } : undefined,
  }) as unknown as ReturnType<typeof getReportsByPubStoreDate<T>>;
};

const inputValidator = z.object({
  stores: z.union([z.array(z.string()), z.string()]).optional(),
  publishers: z.union([z.array(z.string()), z.string()]).optional(),
  dateRange: z
    .object({
      from: z.union([z.date(), z.undefined()]),
      to: z.date().optional(),
    })
    .optional(),
});

export type SalesTotals = {
  grossRevenue: number;
  netRevenue: number;
  returnRevenue: number;
  grossQuantity: number;
  netQuantity: number;
  returnQuantity: number;
};

export type SalesByMonth = Array<{
  month: string;
  grossRevenue: number;
  netRevenue: number;
  returnRevenue: number;
  grossQuantity: number;
  netQuantity: number;
  returnQuantity: number;
}>;

export type SalesByStore = Array<{
  storeName: string;
  storeId: number;
  grossRevenue: number;
  netRevenue: number;
  returnRevenue: number;
  grossQuantity: number;
  netQuantity: number;
  returnQuantity: number;
}>;

export type SalesByPublisher = Array<{
  publisherName: string;
  publisherId: number;
  grossRevenue: number;
  netRevenue: number;
  returnRevenue: number;
  grossQuantity: number;
  netQuantity: number;
  returnQuantity: number;
}>;

function aggregateReportData(
  report: {
    grossQuantity: number | null;
    grossRevenue: string | null;
    netQuantity: number | null;
    netRevenue: string | null;
    periodEnd: string;
    periodStart: string;
    publisherId: number;
    returnQuantity: number | null;
    returnRevenue: string | null;
  },
  existingItem: {
    grossQuantity: number;
    grossRevenue: number;
    netQuantity: number;
    netRevenue: number;
    returnQuantity: number;
    returnRevenue: number;
  },
) {
  if (report.grossRevenue && report.grossRevenue !== "0") {
    existingItem.grossRevenue += Number(report.grossRevenue);
  } else if (
    report.netRevenue &&
    report.returnRevenue &&
    report.netRevenue !== "0" &&
    report.returnRevenue !== "0"
  ) {
    existingItem.grossRevenue +=
      Number(report.netRevenue) + Number(report.returnRevenue);
  } else {
    existingItem.grossRevenue += 0;
  }

  if (report.netRevenue && report.netRevenue !== "0") {
    existingItem.netRevenue += Number(report.netRevenue);
  } else if (
    report.grossRevenue &&
    report.returnRevenue &&
    report.grossRevenue !== "0" &&
    report.returnRevenue !== "0"
  ) {
    existingItem.netRevenue +=
      Number(report.grossRevenue) - Number(report.returnRevenue);
  } else {
    existingItem.netRevenue += 0;
  }

  if (report.returnRevenue && report.returnRevenue !== "0") {
    existingItem.returnRevenue += Number(report.returnRevenue);
  } else if (
    report.grossRevenue &&
    report.netRevenue &&
    report.grossRevenue !== "0" &&
    report.netRevenue !== "0"
  ) {
    existingItem.returnRevenue +=
      Number(report.grossRevenue) - Number(report.netRevenue);
  } else {
    existingItem.returnRevenue += 0;
  }

  if (report.grossQuantity && report.grossQuantity !== 0) {
    existingItem.grossQuantity += report.grossQuantity;
  } else if (report.netQuantity && report.returnQuantity) {
    existingItem.grossQuantity +=
      (report.netQuantity ?? 0) + (report.returnQuantity ?? 0);
  } else {
    existingItem.grossQuantity += 0;
  }

  if (report.netQuantity && report.netQuantity !== 0) {
    existingItem.netQuantity += report.netQuantity;
  } else if (report.grossQuantity && report.returnQuantity) {
    existingItem.netQuantity +=
      (report.grossQuantity ?? 0) - (report.returnQuantity ?? 0);
  } else {
    existingItem.netQuantity += 0;
  }

  if (report.returnQuantity && report.returnQuantity !== 0) {
    existingItem.returnQuantity += report.returnQuantity;
  } else if (report.grossQuantity && report.netQuantity) {
    existingItem.returnQuantity +=
      (report.grossQuantity ?? 0) - (report.netQuantity ?? 0);
  } else {
    existingItem.returnQuantity += 0;
  }
}

export const salesRouter = createTRPCRouter({
  getSaleTotals: publicProcedure
    .input(inputValidator)
    .query(async ({ input }) => {
      const data = await getReportsByPubStoreDate(
        input.stores,
        input.publishers,
        input.dateRange as DateRange,
      );
      return data.reduce(
        (acc, report) => {
          return {
            grossRevenue: acc.grossRevenue + Number(report.grossRevenue),
            netRevenue: acc.netRevenue + Number(report.netRevenue),
            returnRevenue: acc.returnRevenue + Number(report.returnRevenue),
            grossQuantity: acc.grossQuantity + (report.grossQuantity ?? 0),
            netQuantity: acc.netQuantity + (report.netQuantity ?? 0),
            returnQuantity: acc.returnQuantity + (report.returnQuantity ?? 0),
          };
        },
        {
          grossRevenue: 0,
          netRevenue: 0,
          returnRevenue: 0,
          grossQuantity: 0,
          netQuantity: 0,
          returnQuantity: 0,
        },
      );
    }),
  getSalesByMonth: publicProcedure
    .input(inputValidator)
    .query(async ({ input }) => {
      const data = await getReportsByPubStoreDate(
        input.stores,
        input.publishers,
        input.dateRange as DateRange,
      );

      return data.reduce((acc: SalesByMonth, report) => {
        const month = `${report.periodStart}T09:00:00.000Z`; // I hate dates
        const existingItem = acc.find((item) => item.month === month);
        if (existingItem) {
          aggregateReportData(report, existingItem);
        } else {
          acc.push({
            month,
            grossRevenue: Number(report.grossRevenue),
            netRevenue: Number(report.netRevenue),
            returnRevenue: Number(report.returnRevenue),
            grossQuantity: report.grossQuantity ?? 0,
            netQuantity: report.netQuantity ?? 0,
            returnQuantity: report.returnQuantity ?? 0,
          });
        }
        return acc;
      }, []);
    }),

  getSalesByStore: publicProcedure
    .input(inputValidator)
    .query(async ({ input }) => {
      const data = await getReportsByPubStoreDate(
        input.stores,
        input.publishers,
        input.dateRange as DateRange,
        "store",
      );

      return data
        .reduce((acc: SalesByStore, report) => {
          const existingItem = acc.find(
            (item) => item.storeId === report.storeId,
          );
          if (existingItem) {
            aggregateReportData(report, existingItem);
          } else {
            acc.push({
              storeName: report.store.name,
              storeId: report.store.id,
              grossRevenue: Number(report.grossRevenue),
              netRevenue: Number(report.netRevenue),
              returnRevenue: Number(report.returnRevenue),
              grossQuantity: report.grossQuantity ?? 0,
              netQuantity: report.netQuantity ?? 0,
              returnQuantity: report.returnQuantity ?? 0,
            });
          }
          return acc;
        }, [])
        .sort((a, b) => a.storeName.localeCompare(b.storeName));
    }),

  getSalesByPublisher: publicProcedure
    .input(inputValidator)
    .query(async ({ input }) => {
      const data = await getReportsByPubStoreDate(
        input.stores,
        input.publishers,
        input.dateRange as DateRange,
        "publisher",
      );

      return data
        .reduce((acc: SalesByPublisher, report) => {
          const existingItem = acc.find(
            (item) => item.publisherId === report.publisherId,
          );
          if (existingItem) {
            aggregateReportData(report, existingItem);
          } else {
            acc.push({
              publisherName: report.publisher.name,
              publisherId: report.publisher.id,
              grossRevenue: Number(report.grossRevenue),
              netRevenue: Number(report.netRevenue),
              returnRevenue: Number(report.returnRevenue),
              grossQuantity: report.grossQuantity ?? 0,
              netQuantity: report.netQuantity ?? 0,
              returnQuantity: report.returnQuantity ?? 0,
            });
          }
          return acc;
        }, [])
        .sort((a, b) => a.publisherName.localeCompare(b.publisherName));
    }),
});
