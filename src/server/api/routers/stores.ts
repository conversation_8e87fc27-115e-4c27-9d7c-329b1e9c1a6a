import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import { eq, inArray } from "drizzle-orm";
import { stores } from "~/server/db/schema";
import { z } from "zod/v4";

export const storeRouter = createTRPCRouter({
  getAll: publicProcedure.query(async ({ ctx }) => {
    return ctx.db.query.stores.findMany();
  }),

  getIds: publicProcedure
    .input(z.union([z.array(z.string()), z.string()]).optional())
    .query(async ({ ctx, input }) => {
      switch (typeof input) {
        case "string":
          return ctx.db.query.stores.findFirst({
            where: eq(stores.id, Number(input)),
          });
        case "object":
          const numIds = input.map((id) => Number(id));
          return ctx.db.query.stores.findMany({
            where: inArray(stores.id, numIds),
          });
        default:
          return ctx.db.query.stores.findMany();
      }
    }),
});
