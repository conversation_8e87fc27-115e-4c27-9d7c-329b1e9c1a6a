import type { <PERSON>ada<PERSON> } from "next";
import AnalyticsDashboard from "~/components/analytics-dashboard";
import { api, HydrateClient } from "~/trpc/server";

export const metadata: Metadata = {
  title: "Analytics Dashboard",
  description: "View and analyze store performance metrics",
};

export default function DashboardPage() {
  void api.publisher.getAll.prefetch();
  void api.store.getAll.prefetch();

  return (
    <HydrateClient>
      <AnalyticsDashboard />
    </HydrateClient>
  );
}
