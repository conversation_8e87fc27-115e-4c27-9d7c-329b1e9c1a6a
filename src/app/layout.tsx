import "~/styles/globals.css";

import { GeistSans } from "geist/font/sans";
import { type Metadata } from "next";
import { TRPCReactProvider } from "~/trpc/react";
import IsLoggedIn from "~/components/is-logged-in";

export const metadata: Metadata = {
  title: "Analytics",
  description: "",
  icons: [{ rel: "icon", url: "/favicon.svg" }],
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${GeistSans.variable}`}>
      <body>
        <IsLoggedIn>
          <TRPCReactProvider>{children}</TRPCReactProvider>
        </IsLoggedIn>
      </body>
    </html>
  );
}
