import Link from "next/link";
import { ImportSheet } from "~/components/pageSegment/import-sheet";
import { Button } from "~/components/ui/button";
import {
  getAllPublishers,
  getAllReadStores,
  getStoreTypes,
} from "~/server/queries";
import { ChartNoAxesCombined } from "lucide-react";

export default async function HomePage() {
  const publishers = await getAllPublishers();
  const readStores = await getAllReadStores();
  const storeTypes = await getStoreTypes();

  return (
    <main className="flex min-h-screen flex-col items-center justify-center bg-linear-to-b from-[#2e026d] to-[#15162c] text-white">
      <div className="container flex flex-col items-center justify-center gap-12 px-4 py-16">
        <div className="flex items-center gap-2">
          <h1 className="text-5xl font-extrabold tracking-tight text-white sm:text-[5rem]">
            Upload spreadsheet
          </h1>
          <Link href="/">
            <Button variant="outline" size="sm" className="text-foreground">
              <ChartNoAxesCombined className="h-4 w-4" />
              Back
            </Button>
          </Link>
        </div>
        <ImportSheet
          publishers={publishers}
          readStores={readStores}
          storeTypes={storeTypes}
        />
      </div>
    </main>
  );
}
