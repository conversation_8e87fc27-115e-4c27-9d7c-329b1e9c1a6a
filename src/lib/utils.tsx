import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { type DateRange } from "react-day-picker";
import {
  addMonths,
  differenceInMonths,
  endOfMonth,
  format,
  startOfMonth,
  subDays,
  subMonths,
} from "date-fns";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const toTitleCase = (str: string | undefined) => {
  if (!str) return str;
  return str.replace(
    /\w\S*/g,
    (text) => text.charAt(0).toUpperCase() + text.slice(1).toLowerCase(),
  );
};

export const removeBadWords = (str: string | undefined) => {
  const nonoWords = ["ltd", "ltd.", "inc", "inc."];
  if (!str) return str;
  const words = str.split(" ");
  return words
    .filter((word) => !nonoWords.includes(word.toLowerCase()))
    .join(" ");
};

export type RemoveNull<T> = T extends null ? never : T;

export type RemoveNullFromObject<T> = {
  [K in keyof T]: T[K] extends null
    ? never
    : T[K] extends infer U | null
      ? RemoveNull<U>
      : T[K];
};

export const calculateComparisonRange = (
  dateRange: DateRange,
  start?: Date,
) => {
  const { from, to } = dateRange;
  if (!from || !to) {
    throw new Error("Invalid date range");
  }

  const startDate = startOfMonth(from);
  startDate.setHours(0, 0, 0, 0);

  const endDate = endOfMonth(to);
  endDate.setHours(23, 59, 59, 999);

  const numMonths = differenceInMonths(endDate, startDate) + 1; // Add 1 because differenceInMonths is zero-based

  if (start) {
    return {
      from: startOfMonth(start),
      to: endOfMonth(addMonths(start, numMonths - 1)),
    };
  }

  const comparisonEndDate = subDays(startDate, 1);
  const comparisonStartDate = startOfMonth(subMonths(startDate, numMonths));

  return { from: comparisonStartDate, to: comparisonEndDate };
};

/* eslint-disable
    @typescript-eslint/no-explicit-any,
    @typescript-eslint/no-unsafe-member-access,
    @typescript-eslint/no-unsafe-assignment,
    -- recharts only gives any for label props...
  */
export const chartLabels = (
  props: any,
  data: {
    currentMonth: Date;
    comparisonMonth?: Date;
  }[],
  showYear: boolean,
  showComparison: boolean,
) => {
  const currDate = showYear
    ? format(data[props.payload.index]?.currentMonth ?? new Date(), "MMM")
    : format(data[props.payload.index]?.currentMonth ?? new Date(), "MMM yy");
  let prevDate: string | undefined;
  if (showComparison && data[props.payload.index]?.comparisonMonth) {
    prevDate = showYear
      ? format(data[props.payload.index]?.comparisonMonth ?? new Date(), "MMM")
      : format(
          data[props.payload.index]?.comparisonMonth ?? new Date(),
          "MMM yy",
        );
  }

  return (
    <text
      x={props.x}
      y={props.y}
      textAnchor={props.textAnchor}
      orientation={props.orientation}
      fill={props.fill}
    >
      <tspan x={props.x} dy="0.71em">
        {currDate}
      </tspan>
      {prevDate && (
        <tspan x={props.x} dy="1em" fill="#999">
          {prevDate}
        </tspan>
      )}
    </text>
  );
};
/* eslint-enable */

export const formatAddress = (
  address: google.maps.addressValidation.AddressComponent[],
) => {
  return address.reduce((acc: Record<string, string>, curr) => {
    if (!curr.componentType || !curr.componentName) return acc;
    acc[curr.componentType] = curr.componentName;
    return acc;
  }, {});
};
