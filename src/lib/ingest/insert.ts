"use server";

import {
  type foundStoreRow,
  type newStoreRow,
  type resultRow,
} from "~/lib/ingest/common";
import {
  getAliasesFromIdsAndPub,
  getAllPublishers,
  getAllReadStores,
  insertNewAlias,
  insertPeriodReports,
  insertReadStore,
} from "~/server/queries";
import { type DateRange } from "react-day-picker";
import { type storesInsert } from "~/server/db/schema";

export const findExistingStores = async (
  publisherSelected: string,
  rows: resultRow[],
) => {
  const publisherStoreIds = [
    ...new Set(rows.map((row) => row.publisherStoreId)),
  ];

  const publishersMap = await getAllPublishers();
  const publisherId = publishersMap.find(
    (publisher) => publisher.name === publisherSelected,
  )!.id;

  const aliasesFound = await getAliasesFromIdsAndPub(
    publisherStoreIds,
    publisherId,
  );

  return rows.reduce(
    (acc, row) => {
      const isNew = !aliasesFound.some(
        (store) => store.publisherStoreId === row.publisherStoreId,
      );
      const matchedStore = aliasesFound.find(
        (store) => store.publisherStoreId === row.publisherStoreId,
      );

      if (isNew) {
        acc.new.push({ ...row, publisherId: publisherId });
      } else {
        acc.existing.push({
          ...row,
          storeId: matchedStore!.storeId,
          publisherId: publisherId,
        });
      }

      return acc;
    },
    { new: [] as newStoreRow[], existing: [] as foundStoreRow[] },
  );
};

export const insertRecords = async (
  rows: foundStoreRow[],
  period: DateRange | undefined,
) => {
  if (!period?.from || !period.to) {
    return;
  }
  await insertPeriodReports(
    rows.map((row) => {
      return {
        netRevenue: row.netRevenue?.toString(),
        netQuantity: row.netQuantity,
        grossRevenue: row.grossRevenue?.toString(),
        grossQuantity: row.grossQuantity,
        returnRevenue: row.returnRevenue?.toString(),
        returnQuantity: row.returnQuantity,
        periodStart: period.from!.toDateString(),
        periodEnd: period.to!.toDateString(),
        publisherId: row.publisherId,
        storeId: row.storeId,
      };
    }),
  );
};

export const insertNewStore = async (store: storesInsert) => {
  const id = await insertReadStore({
    ...store,
    latitude: store.latitude?.toString(),
    longitude: store.longitude?.toString(),
  });
  return id[0]?.insertedId;
};

export const insertAlias = async (data: insertNewAlias) => {
  await insertNewAlias(data);
};

export const getStores = async () => {
  return getAllReadStores();
};
