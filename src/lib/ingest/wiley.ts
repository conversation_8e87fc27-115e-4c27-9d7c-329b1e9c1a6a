import * as XLSX from "xlsx";
import {type WorkSheet} from "xlsx";
import {z} from "zod/v4";
import type {resultRow} from "~/lib/ingest/common";

export const wiley = async (sheet: WorkSheet) => {
  const data = XLSX.utils.sheet_to_json(sheet, {header: 1, range: 10});
  const res = filterValid(data);
  return res.valid;
};

const wileySchema = z.tuple([
  z.string(),
  z.number().optional(),
  z.number().optional(),
  z.number().optional(),
]).rest(z.any());

type validRow = z.infer<typeof wileySchema>;

const transformRow = (row: validRow): resultRow => {
  const [name, id] = row[0].split("(")
  return {
    publisherStoreId: id!.slice(0, -1),
    identifier: name!.trim(),
    grossRevenue: Number(Math.abs(row[2] ?? 0).toFixed(2)),
  };
}

const filterValid = (data: unknown[]) => {
  const valid: resultRow[] = [];
  const errors: unknown[] = [];

  data.forEach((item) => {
    const result = wileySchema.safeParse(item);
    if (result.success) {
      if (result.data[0] != "Grand Total") valid.push(transformRow(result.data));
    } else {
      errors.push({item, error: result.error});
    }
  });

  return {
    valid,
    errors
  };
}
