import { z } from "zod/v4";
import { type WorkSheet } from "xlsx";
import { anansi } from "~/lib/ingest/anansi";
import { pgc } from "~/lib/ingest/pgc";
import { hgd } from "~/lib/ingest/hgd";
import { wiley } from "~/lib/ingest/wiley";

const _resultSchema = z.object({
  publisherStoreId: z.string(),
  identifier: z.string(),
  identifier2: z.string().optional(),
  address: z.string().optional(),
  netRevenue: z.number().optional(),
  netQuantity: z.number().int().optional(),
  grossRevenue: z.number().optional(),
  grossQuantity: z.number().int().optional(),
  returnRevenue: z.number().optional(),
  returnQuantity: z.number().int().optional(),
});

export type resultRow = z.infer<typeof _resultSchema>;

export type newStoreRow = resultRow & { publisherId: number };
export type foundStoreRow = resultRow & {
  storeId: number;
  publisherId: number;
};

export const publisherToFunction: Record<
  string,
  (sheet: WorkSheet) => Promise<resultRow[]>
> = {
  Anansi: anansi,
  PGC: pgc,
  HGD: hgd,
  Wiley: wiley,
};
