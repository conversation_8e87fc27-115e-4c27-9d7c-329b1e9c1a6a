import * as XLSX from "xlsx";
import { type WorkSheet } from "xlsx";
import { z } from "zod/v4";
import type { resultRow } from "~/lib/ingest/common";

export const hgd = async (sheet: WorkSheet) => {
  const data = XLSX.utils.sheet_to_json(sheet, { range: 1 });
  const res = filterValid(data);
  return res.valid;
};

const hgdSchema = z.object({
  Rep: z.string(),
  Customer: z.string(),
  "Customer ID": z.string(),
  Rep_Comm_Pcnt: z.number(),
  State: z.string(),
  Bill_To_ID: z.string(),
  Customer_Type: z.string(),
  Country: z.string(),
  Academic_Institution: z.coerce.boolean(),
  Title: z.string().optional(),
  Category: z.string(),
  "Sales Value": z.number(),
  "Return Value": z.number(),
  "Return Units": z.number(),
  "Net Sales Value": z.number(),
  "Net Units": z.number(),
  "Commission Cost": z.number(),
});

type validRow = z.infer<typeof hgdSchema>;

const transformRow = (row: validRow): resultRow => {
  return {
    publisherStoreId: row["Customer ID"],
    identifier: row.Customer.trim(),
    grossRevenue: row["Sales Value"],
    grossQuantity: row["Net Units"] + row["Return Units"],
    netRevenue: row["Net Sales Value"],
    netQuantity: row["Net Units"],
    returnRevenue: row["Return Value"],
    returnQuantity: row["Return Units"],
  };
};

const filterValid = (data: unknown[]) => {
  const valid: resultRow[] = [];
  const errors: unknown[] = [];

  data.forEach((item) => {
    const result = hgdSchema.safeParse(item);
    if (result.success) {
      valid.push(transformRow(result.data));
    } else {
      errors.push({ item, error: result.error });
    }
  });

  return {
    valid,
    errors,
  };
};
