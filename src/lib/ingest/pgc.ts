import * as XLSX from "xlsx";
import { type WorkSheet } from "xlsx";
import { z } from "zod/v4";
import type { resultRow } from "~/lib/ingest/common";

export const pgc = async (sheet: WorkSheet) => {
  const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
  const res = filterValid(data);
  return res.valid;
};

const pcgSchema = z
  .tuple([
    z.union([z.string(), z.number()]),
    z.string(),
    z.string(),
    z.string(),
    z.union([z.string(), z.number()]),
    z.string(),
    z.string(),
    z.number(),
    z.number(),
  ])
  .rest(z.any());

type validRow = z.infer<typeof pcgSchema>;

const transformRow = (row: validRow): resultRow => {
  return {
    publisherStoreId: row[0].toString(),
    identifier: row[1].trim(),
    netRevenue: row[7],
    netQuantity: row[8],
  };
};

const filterValid = (data: unknown[]) => {
  const valid: resultRow[] = [];
  const errors: unknown[] = [];

  data.forEach((item) => {
    const result = pcgSchema.safeParse(item);
    if (result.success) {
      valid.push(transformRow(result.data));
    } else {
      errors.push({ item, error: result.error });
    }
  });

  return {
    valid,
    errors,
  };
};
