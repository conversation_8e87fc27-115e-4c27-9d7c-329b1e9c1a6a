import * as XLSX from "xlsx";
import {type WorkSheet} from "xlsx";
import {z} from "zod/v4";
import type {resultRow} from "~/lib/ingest/common";

export const anansi = async (sheet: WorkSheet) => {
  const data = XLSX.utils.sheet_to_json(sheet);
  const res = filterValid(data);
  return res.valid;
};

const anasiSchema = z.object({
  "BillTo#": z.number(),
  "Customer Class": z.string(),
  "Customer Name": z.string(),
  "Customer Name2": z.string(),
  "City": z.string(),
  "Province": z.string(),
  "Net(C$)": z.number(),
  "Net(Qty)": z.number(),
  "Gross(C$)": z.number(),
  "Gross(Qty)": z.number(),
  "Return(C$)": z.number(),
  "Return(Qty)": z.number(),
});

type validRow = z.infer<typeof anasiSchema>;

const transformRow = (row: validRow): resultRow => {
  return {
    publisherStoreId: row["BillTo#"].toString(),
    identifier: row["Customer Name"].trim(),
    identifier2: row["Customer Name2"].trim(),
    address: row.City.trim(),
    netRevenue: Math.abs(row["Net(C$)"]),
    netQuantity: Math.abs(row["Net(Qty)"]),
    grossRevenue: Math.abs(row["Gross(C$)"]),
    grossQuantity: Math.abs(row["Gross(Qty)"]),
    returnRevenue: Math.abs(row["Return(C$)"]),
    returnQuantity: Math.abs(row["Return(Qty)"]),
  };
}

const filterValid = (data: unknown[]) => {
  const valid: resultRow[] = [];
  const errors: unknown[] = [];

  data.forEach((item) => {
    const result = anasiSchema.safeParse(item);
    if (result.success) {
      valid.push(transformRow(result.data));
    } else {
      errors.push({item, error: result.error});
    }
  });

  return {
    valid,
    errors
  };
}