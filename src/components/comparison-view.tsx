import type { DateRange } from "react-day-picker";
import { format } from "date-fns";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { api } from "~/trpc/react";
import { Suspense } from "react";
import { type SalesTotals } from "~/server/api/routers/sales";
import { PercentChange } from "~/components/ui/percent-change";
import { IncompleteData } from "~/components/ui/incomplete-data";

const formatData = (
  current: SalesTotals,
  past: SalesTotals,
): Array<{
  name: string;
  current: number;
  previous: number;
  change: number | null;
  format: "currency" | "number";
  inverseIndicator?: boolean;
}> => {
  return [
    {
      name: "Net Sales",
      current: current.netRevenue ?? 0,
      previous: past.netRevenue ?? 0,
      change: ((current.netRevenue - past.netRevenue) / past.netRevenue) * 100,
      format: "currency",
    },
    {
      name: "Gross Sales",
      current: current.grossRevenue ?? 0,
      previous: past.grossRevenue ?? 0,
      change:
        ((current.grossRevenue - past.grossRevenue) / past.grossRevenue) * 100,
      format: "currency",
    },
    {
      name: "Returns",
      current: current.returnRevenue ?? 0,
      previous: past.returnRevenue ?? 0,
      change:
        ((current.returnRevenue - past.returnRevenue) / past.returnRevenue) *
        100,
      format: "currency",
      inverseIndicator: true,
    },
    {
      name: "Net Quantity",
      current: current.netQuantity ?? 0,
      previous: past.netQuantity ?? 0,
      change:
        ((current.netQuantity - past.netQuantity) / past.netQuantity) * 100,
      format: "number",
    },
    {
      name: "Gross Quantity",
      current: current.grossQuantity ?? 0,
      previous: past.grossQuantity ?? 0,
      change:
        current.grossQuantity && past.grossQuantity
          ? ((current.grossQuantity - past.grossQuantity) /
              past.grossQuantity) *
            100
          : null,
      format: "number",
    },
    {
      name: "Return Quantity",
      current: current.returnQuantity ?? 0,
      previous: past.returnQuantity ?? 0,
      change:
        ((current.returnQuantity - past.returnQuantity) / past.returnQuantity) *
        100,
      format: "number",
      inverseIndicator: true,
    },
  ];
};

interface ComparisonViewProps {
  currentDateRange: DateRange | undefined;
  comparisonDateRange: DateRange | undefined;
  selectedStores: string[];
  selectedPublishers: string[];
}

function ComparisonViewData({
  currentDateRange,
  comparisonDateRange,
  selectedStores,
  selectedPublishers,
}: ComparisonViewProps) {
  const [data] = api.sales.getSaleTotals.useSuspenseQuery({
    dateRange: currentDateRange,
    stores: selectedStores,
    publishers: selectedPublishers,
  });

  const [comparisonData] = api.sales.getSaleTotals.useSuspenseQuery({
    dateRange: comparisonDateRange,
    stores: selectedStores,
    publishers: selectedPublishers,
  });

  const formattedData = formatData(data, comparisonData);

  return formattedData.map((metric) => {
    const change = metric.change == Infinity ? null : metric.change;

    return (
      <TableRow key={metric.name}>
        <TableCell className="font-medium">{metric.name}</TableCell>
        <TableCell>
          {metric.format === "currency"
            ? metric.current.toLocaleString("en-CA", {
                style: "currency",
                currency: "CAD",
              })
            : metric.current.toLocaleString()}
        </TableCell>
        <TableCell className="text-muted-foreground">
          {metric.format === "currency"
            ? metric.previous.toLocaleString("en-CA", {
                style: "currency",
                currency: "CAD",
              })
            : metric.previous.toLocaleString()}
        </TableCell>
        <TableCell>
          <PercentChange
            value={change}
            inverseIndicator={metric.inverseIndicator}
          />
        </TableCell>
      </TableRow>
    );
  });
}

export function ComparisonView({
  currentDateRange,
  comparisonDateRange,
  selectedStores,
  selectedPublishers,
}: ComparisonViewProps) {
  const currentPeriod =
    currentDateRange?.from && currentDateRange.to
      ? `${format(currentDateRange.from, "MMM d, yyyy")} - ${format(currentDateRange.to, "MMM d, yyyy")}`
      : "Current Period";
  const previousPeriod =
    comparisonDateRange?.from && comparisonDateRange.to
      ? `${format(comparisonDateRange.from, "MMM d, yyyy")} - ${format(comparisonDateRange.to, "MMM d, yyyy")}`
      : "Previous Period";

  return (
    <Card>
      <CardHeader>
        <CardTitle>Period Comparison</CardTitle>
        <CardDescription className="flex flex-row items-center justify-between gap-2">
          Comparing {currentPeriod} with {previousPeriod}
          <IncompleteData text={"Gross and return data does not include PGC"} />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Suspense fallback={<div>Loading...</div>}>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Metric</TableHead>
                <TableHead>{currentPeriod}</TableHead>
                <TableHead>{previousPeriod}</TableHead>
                <TableHead>Change</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <ComparisonViewData
                currentDateRange={currentDateRange}
                comparisonDateRange={comparisonDateRange}
                selectedStores={selectedStores}
                selectedPublishers={selectedPublishers}
              />
            </TableBody>
          </Table>
        </Suspense>
      </CardContent>
    </Card>
  );
}
