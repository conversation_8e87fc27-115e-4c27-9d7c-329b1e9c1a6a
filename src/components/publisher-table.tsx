import { Suspense } from "react";
import { type ColumnDef } from "@tanstack/react-table";
import { type SalesByPublisher } from "~/server/api/routers/sales";
import type { DateRange } from "react-day-picker";
import { api } from "~/trpc/react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { IncompleteData } from "~/components/ui/incomplete-data";
import { DataTable, DataTableColumnHeader } from "~/components/ui/data-table";
import { LoadingSpinner } from "~/components/ui/loading-spinner";

export const columns: ColumnDef<SalesByPublisher[number]>[] = [
  {
    accessorKey: "publisherName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Publisher" />
    ),
    cell: (props) => {
      return (
        <div className="w-3xs max-w-3xs truncate font-medium">
          {props.getValue() as string}
        </div>
      );
    },
  },
  {
    accessorKey: "netRevenue",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Net Sales" />
    ),
    cell: (props) => {
      return (props.getValue() as number).toLocaleString("en-CA", {
        style: "currency",
        currency: "CAD",
      });
    },
  },
  {
    accessorKey: "grossRevenue",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Gross Sales" />
    ),
    cell: (props) => {
      return (props.getValue() as number).toLocaleString("en-CA", {
        style: "currency",
        currency: "CAD",
      });
    },
  },
  {
    accessorKey: "returnRevenue",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Returns" />
    ),
    cell: (props) => {
      return (props.getValue() as number).toLocaleString("en-CA", {
        style: "currency",
        currency: "CAD",
      });
    },
  },
  {
    accessorKey: "netQuantity",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Net Quantity" />
    ),
  },
  {
    accessorKey: "grossQuantity",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Gross Quantity" />
    ),
  },
  {
    accessorKey: "returnQuantity",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Return Quantity" />
    ),
  },
];

interface PublisherTableProps {
  dateRange: DateRange | undefined;
  comparisonDateRange: DateRange | undefined;
  showComparison: boolean;
  selectedStores: string[];
  selectedPublishers: string[];
}

function PublisherTableData({
  dateRange,
  comparisonDateRange,
  showComparison,
  selectedStores,
  selectedPublishers,
}: PublisherTableProps) {
  const [filteredData] = api.sales.getSalesByPublisher.useSuspenseQuery({
    stores: selectedStores,
    publishers: selectedPublishers,
    dateRange,
  });

  const comparisonData = api.sales.getSalesByPublisher.useQuery(
    {
      stores: selectedStores,
      publishers: selectedPublishers,
      dateRange: comparisonDateRange,
    },
    {
      enabled: showComparison && !!comparisonDateRange,
    },
  );

  const combinedData = filteredData.map((publisher) => {
    const comparisonPublisher = comparisonData.data?.find(
      (item) => item.publisherId === publisher.publisherId,
    );

    if (!comparisonPublisher) {
      return {
        ...publisher,
        subRows: [],
      };
    }

    return {
      ...publisher,
      subRows: [
        {
          publisherName: "",
          netRevenue: comparisonPublisher.netRevenue ?? 0,
          grossRevenue: comparisonPublisher.grossRevenue ?? 0,
          returnRevenue: comparisonPublisher.returnRevenue ?? 0,
          netQuantity: comparisonPublisher.netQuantity ?? 0,
          grossQuantity: comparisonPublisher.grossQuantity ?? 0,
          returnQuantity: comparisonPublisher.returnQuantity ?? 0,
        },
      ],
    };
  });

  return <DataTable columns={columns} data={combinedData} />;
}

export function PublisherTable({
  dateRange,
  comparisonDateRange,
  showComparison,
  selectedStores,
  selectedPublishers,
}: PublisherTableProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex flex-row items-center justify-between gap-2">
          Publisher Performance
          <IncompleteData text={"Gross and return data does not include PGC"} />
        </CardTitle>
        <CardDescription>
          Performance metrics by publisher for the selected period
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Suspense
          fallback={
            <div className="flex h-16 items-center justify-center">
              <LoadingSpinner />
            </div>
          }
        >
          <PublisherTableData
            dateRange={dateRange}
            comparisonDateRange={comparisonDateRange}
            showComparison={showComparison}
            selectedStores={selectedStores}
            selectedPublishers={selectedPublishers}
          />
        </Suspense>
      </CardContent>
    </Card>
  );
}
