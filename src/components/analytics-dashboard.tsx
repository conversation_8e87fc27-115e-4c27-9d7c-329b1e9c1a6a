"use client";

import { useState } from "react";
import type { DateRange } from "react-day-picker";
import { endOfMonth, startOfMonth, subYears } from "date-fns";

import { DashboardHeader } from "~/components/dashboard-header";
import { DashboardShell } from "~/components/dashboard-shell";
import { FilterBar } from "~/components/filter-bar";
import { MetricCards } from "~/components/metric-cards";
import { RevenueChart } from "~/components/revenue-chart";
import { SalesReturnsChart } from "~/components/sales-returns-chart";
import { QuantityChart } from "~/components/quantity-chart";
import { ComparisonView } from "~/components/comparison-view";
import { PublisherTable } from "~/components/publisher-table";
import { StoreTable } from "~/components/store-table";

export default function AnalyticsDashboard() {
  const [selectedStores, setSelectedStores] = useState<string[]>([]);
  const [selectedPublishers, setSelectedPublishers] = useState<string[]>([]);
  const [selectedReps, setSelectedReps] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date()),
  });
  const [comparisonDateRange, setComparisonDateRange] = useState<
    DateRange | undefined
  >({
    from: subYears(dateRange?.from ?? new Date(), 1),
    to: subYears(dateRange?.to ?? new Date(), 1),
  });
  const [showComparison, setShowComparison] = useState(false);

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Analytics Dashboard"
        text="View and analyze store performance metrics"
      />

      <FilterBar
        selectedStores={selectedStores}
        setSelectedStores={setSelectedStores}
        selectedPublishers={selectedPublishers}
        setSelectedPublishers={setSelectedPublishers}
        selectedReps={selectedReps}
        setSelectedReps={setSelectedReps}
        dateRange={dateRange}
        setDateRange={setDateRange}
        showComparison={showComparison}
        setShowComparison={setShowComparison}
        comparisonDateRange={comparisonDateRange}
        setComparisonDateRange={setComparisonDateRange}
      />

      <div className="grid gap-6">
        <MetricCards
          dateRange={dateRange}
          comparisonDateRange={comparisonDateRange}
          showComparison={showComparison}
          selectedStores={selectedStores}
          selectedPublishers={selectedPublishers}
        />

        {showComparison ? (
          <ComparisonView
            currentDateRange={dateRange}
            comparisonDateRange={comparisonDateRange}
            selectedStores={selectedStores}
            selectedPublishers={selectedPublishers}
          />
        ) : (
          <>
            <div className="grid gap-6 md:grid-cols-2">
              <RevenueChart
                dateRange={dateRange}
                selectedStores={selectedStores}
                selectedPublishers={selectedPublishers}
              />
              <SalesReturnsChart
                dateRange={dateRange}
                selectedStores={selectedStores}
                selectedPublishers={selectedPublishers}
              />
            </div>
            <QuantityChart
              dateRange={dateRange}
              selectedStores={selectedStores}
              selectedPublishers={selectedPublishers}
            />
          </>
        )}

        <StoreTable
          dateRange={dateRange}
          comparisonDateRange={comparisonDateRange}
          showComparison={showComparison}
          selectedPublishers={selectedPublishers}
          selectedStores={selectedStores}
        />

        <PublisherTable
          dateRange={dateRange}
          comparisonDateRange={comparisonDateRange}
          showComparison={showComparison}
          selectedPublishers={selectedPublishers}
          selectedStores={selectedStores}
        />
      </div>
    </DashboardShell>
  );
}
