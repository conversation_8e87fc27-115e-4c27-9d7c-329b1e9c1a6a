import { Suspense } from "react";
import type { DateRange } from "react-day-picker";
import { format } from "date-fns";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { api } from "~/trpc/react";
import { LoadingSpinner } from "~/components/ui/loading-spinner";

interface RevenueChartProps {
  dateRange: DateRange | undefined;
  selectedStores: string[];
  selectedPublishers: string[];
}

function RevenueChartData({
  dateRange,
  selectedStores,
  selectedPublishers,
}: RevenueChartProps) {
  const [data] = api.sales.getSalesByMonth.useSuspenseQuery({
    stores: selectedStores,
    publishers: selectedPublishers,
    dateRange,
  });

  const sortedData = data
    .toSorted((a, b) => a.month.localeCompare(b.month))
    .map((month, index) => ({
      index,
      month: new Date(month.month),
      revenue: month.netRevenue,
    }));

  const allInCurrentYear =
    sortedData[0]?.month?.getFullYear() ===
      sortedData[sortedData.length - 1]?.month?.getFullYear() &&
    sortedData[0]?.month?.getFullYear() === new Date().getFullYear();

  const getXAxisLabel = (index: number) => {
    return allInCurrentYear
      ? format(sortedData[index]!.month, "MMM")
      : format(sortedData[index]!.month, "MMM yy");
  };

  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={sortedData}
          margin={{
            top: 5,
            right: 10,
            left: 10,
            bottom: 0,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            tickLine={false}
            axisLine={false}
            padding={{ left: 10, right: 10 }}
            tickFormatter={getXAxisLabel}
          />
          <YAxis
            tickFormatter={(value: number) => `$${value.toLocaleString()}`}
            tickLine={false}
            axisLine={false}
            tickCount={6}
          />
          <Tooltip
            content={({ active, payload }) => {
              if (!active || !payload?.length) {
                return null;
              }

              const dataPoint =
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                sortedData[payload[0]?.payload.index as number]!;

              return (
                <div className="bg-background rounded-lg border p-2 shadow-sm">
                  {dataPoint.revenue !== undefined && (
                    <div className="flex flex-col">
                      <span className="text-muted-foreground text-[0.70rem] uppercase">
                        {format(
                          dataPoint.month,
                          allInCurrentYear ? "MMM" : "MMM yy",
                        )}
                      </span>
                      <span className="text-muted-foreground font-bold">
                        {dataPoint.revenue.toLocaleString("en-CA", {
                          style: "currency",
                          currency: "CAD",
                        })}
                      </span>
                    </div>
                  )}
                </div>
              );
            }}
          />
          <Line
            type="monotone"
            dataKey="revenue"
            name="Net Sales"
            stroke="#0ea5e9"
            strokeWidth={2}
            activeDot={{ r: 6, style: { fill: "#0ea5e9" } }}
          />
          <Legend />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

export function RevenueChart({
  dateRange,
  selectedStores,
  selectedPublishers,
}: RevenueChartProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Net Sales</CardTitle>
          <CardDescription>Monthly net revenue over time</CardDescription>
        </div>
        <div></div>
      </CardHeader>
      <CardContent>
        <Suspense
          fallback={
            <div className="h-[300px]">
              <div className="text-muted-foreground flex h-full items-center justify-center">
                <LoadingSpinner />
              </div>
            </div>
          }
        >
          <RevenueChartData
            dateRange={dateRange}
            selectedStores={selectedStores}
            selectedPublishers={selectedPublishers}
          />
        </Suspense>
      </CardContent>
    </Card>
  );
}
