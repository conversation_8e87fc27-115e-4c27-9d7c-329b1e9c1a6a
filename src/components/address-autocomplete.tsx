import React, { useMemo, useRef, useState } from "react";
import { useMapsLibrary } from "@vis.gl/react-google-maps";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { formatAddress } from "~/lib/utils";
import { MapPin } from "lucide-react";
import { Card } from "~/components/ui/card";

type Suggestion = google.maps.places.AutocompleteSuggestion;

interface AddressAutofillProps {
  onValidate: (address: Record<string, string>) => void;
}

export function AddressAutofill({ onValidate }: AddressAutofillProps) {
  const [inputValue, setInputValue] = useState("");
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [busy, setBusy] = useState(false);

  // @ts-expect-error beta channel
  const verifyLib = useMapsLibrary("addressValidation");
  const placesLib = useMapsLibrary("places");

  const sessionToken = useMemo(
    () => (placesLib ? new placesLib.AutocompleteSessionToken() : null),
    [placesLib],
  );

  const inputRef = useRef<HTMLInputElement>(null);

  const fetchSuggestions = async (input: string) => {
    if (!placesLib || !sessionToken || input.trim().length < 3) {
      setSuggestions([]);
      return;
    }
    const res =
      await placesLib.AutocompleteSuggestion.fetchAutocompleteSuggestions({
        input,
        includedRegionCodes: ["CA"],
        sessionToken,
      });
    setSuggestions(res.suggestions);
  };

  const handlePick = async (s: Suggestion | undefined) => {
    if (!s?.placePrediction || !placesLib || !sessionToken) return;
    setBusy(true);
    /* eslint-disable */ // beta channel
    try {
      const validated =
        // @ts-expect-error using beta channel
        await verifyLib.AddressValidation.fetchAddressValidation({
          address: {
            addressLines: [s.placePrediction.text.text],
            regionCode: "CA",
          },
          sessionToken,
        });
      console.log(
        "validated",
        formatAddress(validated.address?.components ?? []),
      );
      setInputValue(validated.address?.formattedAddress ?? "");
      setSuggestions([]);
      onValidate(formatAddress(validated.address?.components ?? []));
    } finally {
      setBusy(false);
    }
    /* eslint-enable */
  };

  return (
    <div className="space-y-2">
      <Label htmlFor="address-autofill" className="text-sm font-medium">
        Address
      </Label>
      <div className="relative">
        <div className="flex gap-2">
          <div className="relative flex-1">
            <MapPin className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
            <Input
              id="address-autofill"
              ref={inputRef}
              type="text"
              placeholder="Enter your address..."
              value={inputValue}
              disabled={busy}
              onChange={(e) => {
                const v = e.target.value;
                setInputValue(v);
                void fetchSuggestions(v);
              }}
              autoComplete="aaa"
              className="pr-4 pl-10"
            />
          </div>
        </div>

        {suggestions.length > 0 && !busy && (
          <Card className="absolute z-50 mt-1 w-full overflow-hidden border shadow-lg">
            <div className="max-h-60 overflow-y-auto">
              {suggestions.map((s, i) => (
                <div
                  key={i}
                  className="hover:bg-accent hover:text-accent-foreground flex cursor-pointer items-center gap-3 border-b px-4 py-3 text-sm transition-colors last:border-b-0"
                  onMouseDown={(e) => {
                    e.preventDefault();
                    void handlePick(s);
                  }}
                >
                  <span className="truncate">
                    {s.placePrediction?.text.text}
                  </span>
                </div>
              ))}
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
