import { Suspense } from "react";
import { type ColumnDef } from "@tanstack/react-table";
import { type SalesByStore } from "~/server/api/routers/sales";
import type { DateRange } from "react-day-picker";
import { api } from "~/trpc/react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { IncompleteData } from "~/components/ui/incomplete-data";
import { DataTable, DataTableColumnHeader } from "~/components/ui/data-table";
import { LoadingSpinner } from "~/components/ui/loading-spinner";

export const columns: ColumnDef<SalesByStore[number]>[] = [
  {
    accessorKey: "storeName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Store" />
    ),
    cell: (props) => {
      return (
        <div className="w-3xs max-w-3xs truncate font-medium">
          {props.getValue() as string}
        </div>
      );
    },
  },
  {
    accessorKey: "netRevenue",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Net Sales" />
    ),
    cell: (props) => {
      return (props.getValue() as number).toLocaleString("en-CA", {
        style: "currency",
        currency: "CAD",
      });
    },
  },
  {
    accessorKey: "grossRevenue",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Gross Sales" />
    ),
    cell: (props) => {
      return (props.getValue() as number).toLocaleString("en-CA", {
        style: "currency",
        currency: "CAD",
      });
    },
  },
  {
    accessorKey: "returnRevenue",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Returns" />
    ),
    cell: (props) => {
      return (props.getValue() as number).toLocaleString("en-CA", {
        style: "currency",
        currency: "CAD",
      });
    },
  },
  {
    accessorKey: "netQuantity",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Net Quantity" />
    ),
  },
  {
    accessorKey: "grossQuantity",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Gross Quantity" />
    ),
  },
  {
    accessorKey: "returnQuantity",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Return Quantity" />
    ),
  },
];

interface StoreTableProps {
  dateRange: DateRange | undefined;
  comparisonDateRange: DateRange | undefined;
  showComparison: boolean;
  selectedStores: string[];
  selectedPublishers: string[];
}

function StoreTableData({
  dateRange,
  comparisonDateRange,
  showComparison,
  selectedStores,
  selectedPublishers,
}: StoreTableProps) {
  const [filteredData] = api.sales.getSalesByStore.useSuspenseQuery({
    stores: selectedStores,
    publishers: selectedPublishers,
    dateRange,
  });

  const comparisonData = api.sales.getSalesByStore.useQuery(
    {
      stores: selectedStores,
      publishers: selectedPublishers,
      dateRange: comparisonDateRange,
    },
    {
      enabled: showComparison && !!comparisonDateRange,
    },
  );

  const combinedData = filteredData.map((store) => {
    const comparisonStore = comparisonData.data?.find(
      (item) => item.storeId === store.storeId,
    );

    if (!showComparison) {
      return {
        ...store,
        subRows: [],
      };
    }

    return {
      ...store,
      subRows: [
        {
          storeName: "",
          netRevenue: comparisonStore?.netRevenue ?? 0,
          grossRevenue: comparisonStore?.grossRevenue ?? 0,
          returnRevenue: comparisonStore?.returnRevenue ?? 0,
          netQuantity: comparisonStore?.netQuantity ?? 0,
          grossQuantity: comparisonStore?.grossQuantity ?? 0,
          returnQuantity: comparisonStore?.returnQuantity ?? 0,
        },
      ],
    };
  });

  return (
    <DataTable
      columns={columns}
      data={combinedData}
      options={{
        filter: { columnId: "storeName", placeholder: "Search stores..." },
        pagination: true,
      }}
    />
  );
}

export function StoreTable({
  dateRange,
  selectedStores,
  comparisonDateRange,
  showComparison,
  selectedPublishers,
}: StoreTableProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex flex-row items-center justify-between gap-2">
          Store Performance
          <IncompleteData text={"Gross and return data does not include PGC"} />
        </CardTitle>
        <CardDescription>
          Performance metrics by store for the selected period
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Suspense
          fallback={
            <div className="flex h-16 items-center justify-center">
              <LoadingSpinner />
            </div>
          }
        >
          <StoreTableData
            dateRange={dateRange}
            comparisonDateRange={comparisonDateRange}
            showComparison={showComparison}
            selectedStores={selectedStores}
            selectedPublishers={selectedPublishers}
          />
        </Suspense>
      </CardContent>
    </Card>
  );
}
