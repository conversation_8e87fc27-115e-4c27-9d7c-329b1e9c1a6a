import { Suspense } from "react";
import type { DateRange } from "react-day-picker";
import { ArrowUpIcon, DollarSign, Package, ShoppingCart } from "lucide-react";

import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { api } from "~/trpc/react";
import { IncompleteData } from "~/components/ui/incomplete-data";
import { LoadingSpinner } from "~/components/ui/loading-spinner";
import { subYears } from "date-fns";
import { PercentChange } from "~/components/ui/percent-change";

interface MetricCardsProps {
  dateRange: DateRange | undefined;
  comparisonDateRange: DateRange | undefined;
  showComparison: boolean;
  selectedStores: string[];
  selectedPublishers: string[];
}

function MetricCardData({
  dateRange,
  comparisonDateRange,
  showComparison,
  selectedStores,
  selectedPublishers,
  metric,
}: MetricCardsProps & {
  metric: "netRevenue" | "grossRevenue" | "returnRevenue" | "netQuantity";
}) {
  const [data] = api.sales.getSaleTotals.useSuspenseQuery({
    dateRange: dateRange,
    stores: selectedStores,
    publishers: selectedPublishers,
  });

  const compareRange = showComparison
    ? comparisonDateRange
    : {
        from: subYears(dateRange?.from ?? new Date(), 1),
        to: subYears(dateRange?.to ?? new Date(), 1),
      };
  const [comparisonData] = api.sales.getSaleTotals.useSuspenseQuery({
    dateRange: compareRange,
    stores: selectedStores,
    publishers: selectedPublishers,
  });

  const value = data[metric];
  const comparisonValue =
    value && comparisonData[metric]
      ? ((value - comparisonData[metric]) / comparisonData[metric]) * 100
      : 0;

  const Compare = () => (
    <div className="flex items-center space-x-2 text-sm">
      <PercentChange
        value={comparisonValue}
        inverseIndicator={metric === "returnRevenue"}
      />
      <span className="text-muted-foreground">
        from previous {showComparison ? "period" : "year"}
      </span>
    </div>
  );

  if (
    metric === "netRevenue" ||
    metric === "grossRevenue" ||
    metric === "returnRevenue"
  ) {
    return (
      <>
        <div className="text-2xl font-bold">
          {value.toLocaleString("en-CA", {
            style: "currency",
            currency: "CAD",
          })}
        </div>
        <Compare />
      </>
    );
  }

  return (
    <>
      <div className="text-2xl font-bold">{value.toLocaleString("en-CA")}</div>
      <Compare />
    </>
  );
}

export function MetricCards({
  dateRange,
  comparisonDateRange,
  showComparison,
  selectedStores,
  selectedPublishers,
}: MetricCardsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Net Sales</CardTitle>
          <DollarSign className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <Suspense
            fallback={
              <>
                <div className="flex items-center">
                  <LoadingSpinner className="h-8 w-8" />
                </div>

                <div className="flex items-center space-x-2 text-sm">
                  <span className="text-green-500">
                    <ArrowUpIcon className="h-4 w-4" />
                  </span>
                  <span className="text-green-500">- %</span>
                  <span className="text-muted-foreground">
                    from previous period
                  </span>
                </div>
              </>
            }
          >
            <MetricCardData
              dateRange={dateRange}
              comparisonDateRange={comparisonDateRange}
              showComparison={showComparison}
              selectedStores={selectedStores}
              selectedPublishers={selectedPublishers}
              metric="netRevenue"
            />
          </Suspense>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Gross Sales</CardTitle>
          <div className="flex flex-row items-center gap-2">
            <ShoppingCart className="text-muted-foreground h-4 w-4" />
            <IncompleteData />
          </div>
        </CardHeader>
        <CardContent>
          <Suspense
            fallback={
              <>
                <div className="flex items-center">
                  <LoadingSpinner className="h-8 w-8" />
                </div>

                <div className="flex items-center space-x-2 text-sm">
                  <span className="text-green-500">
                    <ArrowUpIcon className="h-4 w-4" />
                  </span>
                  <span className="text-green-500">- %</span>
                  <span className="text-muted-foreground">
                    from previous period
                  </span>
                </div>
              </>
            }
          >
            <MetricCardData
              dateRange={dateRange}
              comparisonDateRange={comparisonDateRange}
              showComparison={showComparison}
              selectedStores={selectedStores}
              selectedPublishers={selectedPublishers}
              metric="grossRevenue"
            />
          </Suspense>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Returns</CardTitle>
          <div className="flex flex-row items-center gap-2">
            <Package className="text-muted-foreground h-4 w-4" />
            <IncompleteData />
          </div>
        </CardHeader>
        <CardContent>
          <Suspense
            fallback={
              <>
                <div className="flex items-center">
                  <LoadingSpinner className="h-8 w-8" />
                </div>

                <div className="flex items-center space-x-2 text-sm">
                  <span className="text-green-500">
                    <ArrowUpIcon className="h-4 w-4" />
                  </span>
                  <span className="text-green-500">- %</span>
                  <span className="text-muted-foreground">
                    from previous period
                  </span>
                </div>
              </>
            }
          >
            <MetricCardData
              dateRange={dateRange}
              comparisonDateRange={comparisonDateRange}
              showComparison={showComparison}
              selectedStores={selectedStores}
              selectedPublishers={selectedPublishers}
              metric="returnRevenue"
            />
          </Suspense>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Net Quantity</CardTitle>
          <Package className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <Suspense
            fallback={
              <>
                <div className="flex items-center">
                  <LoadingSpinner className="h-8 w-8" />
                </div>

                <div className="flex items-center space-x-2 text-sm">
                  <span className="text-green-500">
                    <ArrowUpIcon className="h-4 w-4" />
                  </span>
                  <span className="text-green-500">- %</span>
                  <span className="text-muted-foreground">
                    from previous period
                  </span>
                </div>
              </>
            }
          >
            <MetricCardData
              dateRange={dateRange}
              comparisonDateRange={comparisonDateRange}
              showComparison={showComparison}
              selectedStores={selectedStores}
              selectedPublishers={selectedPublishers}
              metric="netQuantity"
            />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
