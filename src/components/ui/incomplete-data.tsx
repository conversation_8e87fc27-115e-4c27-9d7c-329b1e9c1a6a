import { TriangleAlert } from "lucide-react";
import {
  Too<PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { cn } from "~/lib/utils";

export function IncompleteData({
  className,
  text = "Does not include data from PGC",
}: {
  className?: string;
  text?: string;
}) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={150}>
        <TooltipTrigger>
          <TriangleAlert className={cn("h-8 w-8 text-amber-200", className)} />
        </TooltipTrigger>
        <TooltipContent>
          <p>{text}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
