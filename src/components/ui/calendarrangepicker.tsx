import "client-only";

import * as React from "react";
import {
  endOfMonth,
  endOfWeek,
  format,
  startOfMonth,
  startOfWeek,
} from "date-fns";
import { type DateRange, rangeIncludesDate } from "react-day-picker";

import { Calendar } from "~/components/ui/calendar";
import { Card, CardContent } from "~/components/ui/card";

interface CalendarRangePickerProps {
  dateRange: DateRange | undefined;
  setDateRange: (dateRange: DateRange | undefined) => void;
  className?: string;
}

export function CalendarRangePicker({
  dateRange,
  setDateRange,
  className,
}: CalendarRangePickerProps) {
  const handleSelect = (day: Date) => {
    if (!dateRange?.from || (dateRange.from && dateRange.to)) {
      if (day.getDate() === 1) {
        // If it's the first day of the month, select the whole month
        setDateRange({
          from: startOfMonth(day),
          to: endOfMonth(day),
        });
      } else if (day.getDay() === 0) {
        // If it's Sunday, select the whole week
        setDateRange({
          from: startOfWeek(day),
          to: endOfWeek(day),
        });
      } else {
        setDateRange({
          from: day,
          to: undefined,
        });
      }
    } else {
      if (day < dateRange.from) {
        // No backward ranges
        setDateRange({
          from: day,
          to: dateRange.from,
        });
      } else {
        setDateRange({
          from: dateRange.from,
          to: day,
        });
      }
    }
  };

  return (
    <Card className={className}>
      <CardContent className="p-3">
        <Calendar
          numberOfMonths={2}
          modifiers={{
            selected: dateRange,
            range_start: dateRange?.from,
            range_end: dateRange?.to,
            range_middle: (date: Date) =>
              dateRange ? rangeIncludesDate(dateRange, date, true) : false,
          }}
          onDayClick={handleSelect}
          className="rounded-md border shadow-sm"
        />
        <p className="text-muted-foreground mt-4 text-center text-sm">
          {dateRange?.from ? (
            <>
              <span className="text-foreground font-medium">
                {format(dateRange.from, "MMM do")}
              </span>
              {dateRange.to && (
                <>
                  {" to "}
                  <span className="text-foreground font-medium">
                    {format(dateRange.to, "MMM do")}
                  </span>
                </>
              )}
            </>
          ) : (
            "No selection"
          )}
        </p>
      </CardContent>
    </Card>
  );
}
