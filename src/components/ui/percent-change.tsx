import { ArrowDownIcon, ArrowUpIcon } from "lucide-react";

export function PercentChange({
  value,
  inverseIndicator = false,
}: {
  value: number | null;
  inverseIndicator?: boolean;
}) {
  const isPositive = value ? (inverseIndicator ? value < 0 : value > 0) : true;

  let arrow;
  if (isPositive) {
    if (inverseIndicator) {
      arrow = <ArrowDownIcon className="h-4 w-4" />;
    } else {
      arrow = <ArrowUpIcon className="h-4 w-4" />;
    }
  } else {
    if (inverseIndicator) {
      arrow = <ArrowUpIcon className="h-4 w-4" />;
    } else {
      arrow = <ArrowDownIcon className="h-4 w-4" />;
    }
  }

  return (
    <div className="flex items-center gap-1">
      <span className={isPositive ? "text-green-500" : "text-red-500"}>
        {arrow}
      </span>
      <span className={isPositive ? "text-green-500" : "text-red-500"}>
        {value ? Math.abs(value).toFixed(1) : "- "}%
      </span>
    </div>
  );
}
