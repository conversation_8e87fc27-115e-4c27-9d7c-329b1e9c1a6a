import { useState } from "react";
import { type DateRange } from "react-day-picker";
import {
  endOfMonth,
  endOfQuarter,
  format,
  startOfMonth,
  startOfQuarter,
  startOfYear,
  subMonths,
  subYears,
} from "date-fns";
import {
  CalendarIcon,
  Check,
  ChevronsUpDown,
  FilterIcon,
  RotateCw,
  X,
} from "lucide-react";

import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from "~/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { Toggle } from "~/components/ui/toggle";
import { api } from "~/trpc/react";
import { calculateComparisonRange, cn } from "~/lib/utils";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";

interface FilterBarProps {
  selectedStores: string[];
  setSelectedStores: (stores: string[]) => void;
  selectedPublishers: string[];
  setSelectedPublishers: (publishers: string[]) => void;
  selectedReps: string[];
  setSelectedReps: (reps: string[]) => void;
  dateRange: DateRange | undefined;
  setDateRange: (range: DateRange | undefined) => void;
  showComparison: boolean;
  setShowComparison: (show: boolean) => void;
  comparisonDateRange: DateRange | undefined;
  setComparisonDateRange: (range: DateRange | undefined) => void;
}

export function FilterBar({
  selectedStores,
  setSelectedStores,
  selectedPublishers,
  setSelectedPublishers,
  selectedReps,
  setSelectedReps,
  dateRange,
  setDateRange,
  showComparison,
  setShowComparison,
  comparisonDateRange,
  setComparisonDateRange,
}: FilterBarProps) {
  const [publishers] = api.publisher.getAll.useSuspenseQuery();
  const [stores] = api.store.getAll.useSuspenseQuery();
  const reps: { id: number; name: string }[] = [
    { id: 1, name: "John Doe" },
    { id: 2, name: "Jane Doe" },
  ]; // api.user.getAll.useSuspenseQuery();

  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [storeSearchTerm, setStoreSearchTerm] = useState("");
  const [publisherSearchTerm, setPublisherSearchTerm] = useState("");
  const [storeCommandOpen, setStoreCommandOpen] = useState(false);
  const [publisherCommandOpen, setPublisherCommandOpen] = useState(false);
  const [repSearchTerm, setRepSearchTerm] = useState("");
  const [repCommandOpen, setRepCommandOpen] = useState(false);

  // Get selected store names for display
  const selectedStoreNames = selectedStores.map(
    (id) => stores.find((store) => store.id.toString() === id)?.name ?? id,
  );

  // Get selected publisher names for display
  const selectedPublisherNames = selectedPublishers.map(
    (id) =>
      publishers?.find((publisher) => publisher.id.toString() === id)?.name ??
      id,
  );

  const selectedRepNames = selectedReps.map(
    (id) => reps.find((rep) => rep.id.toString() === id)?.name ?? id,
  );

  const removeStore = (storeId: string) => {
    setSelectedStores(selectedStores.filter((id) => id !== storeId));
  };

  const removePublisher = (publisherId: string) => {
    setSelectedPublishers(
      selectedPublishers.filter((id) => id !== publisherId),
    );
  };

  const removeRep = (repId: string) => {
    setSelectedReps(selectedReps.filter((id) => id !== repId));
  };

  const clearStoreSelection = () => {
    setSelectedStores([]);
  };

  const clearPublisherSelection = () => {
    setSelectedPublishers([]);
  };

  const clearRepSelection = () => {
    setSelectedReps([]);
  };

  const handleRangeSelect = (props: {
    from?: Date;
    to?: Date;
    comparison?: boolean;
  }) => {
    const { from, to, comparison } = props;
    if (!from && !to) return;

    const setFunction = comparison ? setComparisonDateRange : setDateRange;
    console.log("comparison", comparison);
    console.log("setFunction", comparison ? "comparison" : "current");
    const range = comparison ? comparisonDateRange : dateRange;

    if (from) {
      setFunction({
        from: startOfMonth(from),
        to: range?.to,
      });
    }
    if (to) {
      setFunction({
        from: range?.from,
        to: endOfMonth(to),
      });
    }
    if (range?.from && range.to && range.from > range.to) {
      setFunction({
        from: range.to,
        to: range.from,
      });
    }
  };

  const years = [];
  for (let i = new Date().getFullYear(); i >= 2020; i--) {
    years.push(i);
  }

  const months = [];
  for (let i = 0; i <= 11; i++) {
    const monthName = format(new Date(0, i), "MMM");
    months.push({ monthName, monthNumber: i });
  }

  return (
    <>
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 gap-1"
            onClick={() => setIsFiltersOpen(!isFiltersOpen)}
          >
            <FilterIcon className="h-4 w-4" />
            Filters
            <span className="bg-primary text-primary-foreground ml-1 rounded-full px-1.5 py-0.5 text-xs">
              {selectedStores.length + selectedPublishers.length > 0
                ? selectedStores.length + selectedPublishers.length
                : "All"}
            </span>
          </Button>

          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-8 justify-start gap-1 text-left font-normal"
              >
                <CalendarIcon className="h-4 w-4" />
                {dateRange?.from && dateRange?.to
                  ? `${format(dateRange.from, "MMM d, yy")} - ${format(dateRange.to, "MMM d, yy")}`
                  : "Select date range"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <div className="grid grid-cols-[2em_1fr_1fr] gap-2 p-3">
                <Label className="justify-self-end">From</Label>
                <Select
                  onValueChange={(value) => {
                    const year = parseInt(value, 10);
                    const month = dateRange?.from?.getMonth() ?? 0;
                    const newDate = new Date(year, month, 15);
                    handleRangeSelect({ from: newDate });
                  }}
                  value={dateRange?.from?.getFullYear().toString() ?? ""}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select a year" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  onValueChange={(value) => {
                    const month = parseInt(value, 10);
                    const year =
                      dateRange?.from?.getFullYear() ??
                      new Date().getFullYear();
                    const newDate = new Date(year, month, 15);
                    handleRangeSelect({ from: newDate });
                  }}
                  value={dateRange?.from?.getMonth().toString() ?? ""}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select a month" />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map((month) => (
                      <SelectItem
                        key={month.monthNumber}
                        value={month.monthNumber.toString()}
                      >
                        {month.monthName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Label className="justify-self-end">To</Label>
                <Select
                  onValueChange={(value) => {
                    const year = parseInt(value, 10);
                    const month = dateRange?.to?.getMonth() ?? 11;
                    const newDate = new Date(year, month, 15);
                    handleRangeSelect({ to: newDate });
                  }}
                  value={dateRange?.to?.getFullYear().toString() ?? ""}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select a year" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  onValueChange={(value) => {
                    const month = parseInt(value, 10);
                    const year =
                      dateRange?.to?.getFullYear() ?? new Date().getFullYear();
                    const newDate = new Date(year, month, 15);
                    handleRangeSelect({ to: newDate });
                  }}
                  value={dateRange?.to?.getMonth().toString() ?? ""}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select a month" />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map((month) => (
                      <SelectItem
                        key={month.monthNumber}
                        value={month.monthNumber.toString()}
                      >
                        {month.monthName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center justify-between border-t p-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setDateRange({
                      from: startOfMonth(new Date()),
                      to: endOfMonth(new Date()),
                    })
                  }
                >
                  This month
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setDateRange({
                      from: startOfMonth(subMonths(new Date(), 1)),
                      to: endOfMonth(subMonths(new Date(), 1)),
                    })
                  }
                >
                  Last Month
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setDateRange({
                      from: startOfQuarter(new Date()),
                      to: endOfQuarter(new Date()),
                    })
                  }
                >
                  This Quarter
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setDateRange({
                      from: startOfYear(new Date()),
                      to: endOfMonth(new Date()),
                    })
                  }
                >
                  Year to Date
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          <div>
            <Toggle
              variant="outline"
              size="sm"
              pressed={showComparison}
              onPressedChange={setShowComparison}
              className={cn(
                "h-8 gap-1",
                showComparison ? "rounded-r-none" : "",
              )}
            >
              <RotateCw className="h-4 w-4" />
              Compare
            </Toggle>

            {showComparison && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 justify-start gap-1 rounded-l-none border-l-0 text-left font-normal"
                  >
                    <CalendarIcon className="h-4 w-4" />
                    {comparisonDateRange?.from && comparisonDateRange?.to
                      ? `${format(comparisonDateRange.from, "MMM d, yy")} - ${format(comparisonDateRange.to, "MMM d, yy")}`
                      : "Select comparison period"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <div className="grid grid-cols-[2em_1fr_1fr] gap-2 p-3">
                    <Label className="justify-self-end">From</Label>
                    <Select
                      onValueChange={(value) => {
                        const year = parseInt(value, 10);
                        const month = dateRange?.from?.getMonth() ?? 0;
                        const newDate = new Date(year, month, 15);
                        handleRangeSelect({ from: newDate, comparison: true });
                      }}
                      value={
                        comparisonDateRange?.from?.getFullYear().toString() ??
                        ""
                      }
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select a year" />
                      </SelectTrigger>
                      <SelectContent>
                        {years.map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Select
                      onValueChange={(value) => {
                        const month = parseInt(value, 10);
                        const year =
                          dateRange?.from?.getFullYear() ??
                          new Date().getFullYear();
                        const newDate = new Date(year, month, 15);
                        handleRangeSelect({ from: newDate, comparison: true });
                      }}
                      value={
                        comparisonDateRange?.from?.getMonth().toString() ?? ""
                      }
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select a month" />
                      </SelectTrigger>
                      <SelectContent>
                        {months.map((month) => (
                          <SelectItem
                            key={month.monthNumber}
                            value={month.monthNumber.toString()}
                          >
                            {month.monthName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Label className="justify-self-end">To</Label>
                    <Select
                      onValueChange={(value) => {
                        const year = parseInt(value, 10);
                        const month = dateRange?.to?.getMonth() ?? 11;
                        const newDate = new Date(year, month, 15);
                        handleRangeSelect({ to: newDate, comparison: true });
                      }}
                      value={
                        comparisonDateRange?.to?.getFullYear().toString() ?? ""
                      }
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select a year" />
                      </SelectTrigger>
                      <SelectContent>
                        {years.map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Select
                      onValueChange={(value) => {
                        const month = parseInt(value, 10);
                        const year =
                          dateRange?.to?.getFullYear() ??
                          new Date().getFullYear();
                        const newDate = new Date(year, month, 15);
                        handleRangeSelect({ to: newDate, comparison: true });
                      }}
                      value={
                        comparisonDateRange?.to?.getMonth().toString() ?? ""
                      }
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select a month" />
                      </SelectTrigger>
                      <SelectContent>
                        {months.map((month) => (
                          <SelectItem
                            key={month.monthNumber}
                            value={month.monthNumber.toString()}
                          >
                            {month.monthName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center justify-between border-t p-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const now = new Date();
                        if (dateRange?.from && dateRange.to) {
                          setComparisonDateRange(
                            calculateComparisonRange(dateRange),
                          );
                        } else {
                          setComparisonDateRange({
                            from: subMonths(now, 2),
                            to: subMonths(now, 1),
                          });
                        }
                      }}
                    >
                      Previous Period
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const now = new Date();
                        setComparisonDateRange({
                          from: subYears(dateRange?.from ?? now, 1),
                          to: subYears(dateRange?.to ?? now, 1),
                        });
                      }}
                    >
                      Previous Year
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>
      </div>

      {isFiltersOpen && (
        <Card className="mt-2">
          <CardContent className="p-4">
            <div className="grid gap-6 md:grid-cols-3">
              <div>
                <div className="mb-2 flex items-center justify-between">
                  <h3 className="font-medium">Stores</h3>
                  <div className="flex gap-2">
                    {selectedStores.length > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearStoreSelection}
                      >
                        Clear All
                      </Button>
                    )}
                  </div>
                </div>

                <Popover
                  open={storeCommandOpen}
                  onOpenChange={setStoreCommandOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={storeCommandOpen}
                      className="w-full justify-between"
                    >
                      {selectedStores.length > 0
                        ? `${selectedStores.length} store${selectedStores.length > 1 ? "s" : ""} selected`
                        : "Select stores..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Command>
                      <CommandInput
                        placeholder="Search stores..."
                        value={storeSearchTerm}
                        onValueChange={setStoreSearchTerm}
                      />
                      <CommandList className="max-h-[300px] overflow-auto">
                        <CommandEmpty>No stores found.</CommandEmpty>
                        {stores
                          .toSorted((a, b) => a.name.localeCompare(b.name))
                          .slice(0, 100)
                          .map((store) => (
                            <CommandItem
                              key={store.id}
                              value={store.name}
                              onSelect={() => {
                                setSelectedStores(
                                  selectedStores.includes(store.id.toString())
                                    ? selectedStores.filter(
                                        (id) => id !== store.id.toString(),
                                      )
                                    : [...selectedStores, store.id.toString()],
                                );
                              }}
                            >
                              <Check
                                className={`mr-2 h-4 w-4 ${
                                  selectedStores.includes(store.id.toString())
                                    ? "opacity-100"
                                    : "opacity-0"
                                }`}
                              />
                              {store.name}
                            </CommandItem>
                          ))}
                        {stores.length > 100 && (
                          <div className="text-muted-foreground px-2 py-2 text-xs">
                            Showing 100 of {stores.length} stores. Refine your
                            search to see more.
                          </div>
                        )}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>

                {selectedStores.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {selectedStoreNames.slice(0, 10).map((name, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {name}
                        <X
                          className="pointer-events-auto! h-3 w-3 cursor-pointer"
                          onClick={() => removeStore(selectedStores[index]!)}
                        />
                      </Badge>
                    ))}
                    {selectedStores.length > 10 && (
                      <Badge variant="secondary">
                        +{selectedStores.length - 10} more
                      </Badge>
                    )}
                  </div>
                )}
              </div>

              <div>
                <div className="mb-2 flex items-center justify-between">
                  <h3 className="font-medium">Publishers</h3>
                  <div className="flex gap-2">
                    {selectedPublishers.length > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearPublisherSelection}
                      >
                        Clear All
                      </Button>
                    )}
                  </div>
                </div>

                <Popover
                  open={publisherCommandOpen}
                  onOpenChange={setPublisherCommandOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={publisherCommandOpen}
                      className="w-full justify-between"
                    >
                      {selectedPublishers.length > 0
                        ? `${selectedPublishers.length} publisher${selectedPublishers.length > 1 ? "s" : ""} selected`
                        : "Select publishers..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Command>
                      <CommandInput
                        placeholder="Search publishers..."
                        value={publisherSearchTerm}
                        onValueChange={setPublisherSearchTerm}
                      />
                      <CommandList>
                        <CommandEmpty>No publishers found.</CommandEmpty>
                        {publishers
                          ?.toSorted((a, b) => a.name.localeCompare(b.name))
                          .map((publisher) => (
                            <CommandItem
                              key={publisher.id}
                              value={publisher.name}
                              onSelect={() => {
                                setSelectedPublishers(
                                  selectedPublishers.includes(
                                    publisher.id.toString(),
                                  )
                                    ? selectedPublishers.filter(
                                        (id) => id !== publisher.id.toString(),
                                      )
                                    : [
                                        ...selectedPublishers,
                                        publisher.id.toString(),
                                      ],
                                );
                              }}
                            >
                              <Check
                                className={`mr-2 h-4 w-4 ${
                                  selectedPublishers.includes(
                                    publisher.id.toString(),
                                  )
                                    ? "opacity-100"
                                    : "opacity-0"
                                }`}
                              />
                              {publisher.name}
                            </CommandItem>
                          ))}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>

                {selectedPublishers.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {selectedPublisherNames.map((name, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {name}
                        <X
                          className="pointer-events-auto! h-3 w-3 cursor-pointer"
                          onClick={() =>
                            removePublisher(selectedPublishers[index] ?? "")
                          }
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              <div>
                <div className="mb-2 flex items-center justify-between">
                  <h3 className="font-medium">Reps</h3>
                  <div className="flex gap-2">
                    {selectedReps.length > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearRepSelection}
                      >
                        Clear All
                      </Button>
                    )}
                  </div>
                </div>

                <Popover open={repCommandOpen} onOpenChange={setRepCommandOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={repCommandOpen}
                      className="w-full justify-between"
                    >
                      {selectedReps.length > 0
                        ? `${selectedReps.length} rep${selectedReps.length > 1 ? "s" : ""} selected`
                        : "Select reps..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Command>
                      <CommandInput
                        placeholder="Search reps..."
                        value={repSearchTerm}
                        onValueChange={setRepSearchTerm}
                      />
                      <CommandList>
                        <CommandEmpty>No reps found.</CommandEmpty>
                        {reps
                          ?.toSorted((a, b) => a.name.localeCompare(b.name))
                          .map((rep) => (
                            <CommandItem
                              key={rep.id}
                              value={rep.name}
                              onSelect={() => {
                                setSelectedReps(
                                  selectedReps.includes(rep.id.toString())
                                    ? selectedReps.filter(
                                        (id) => id !== rep.id.toString(),
                                      )
                                    : [...selectedReps, rep.id.toString()],
                                );
                              }}
                            >
                              <Check
                                className={`mr-2 h-4 w-4 ${
                                  selectedReps.includes(rep.id.toString())
                                    ? "opacity-100"
                                    : "opacity-0"
                                }`}
                              />
                              {rep.name}
                            </CommandItem>
                          ))}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>

                {selectedReps.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {selectedRepNames.map((name, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {name}
                        <X
                          className="pointer-events-auto! h-3 w-3 cursor-pointer"
                          onClick={() => removeRep(selectedReps[index] ?? "")}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
}
