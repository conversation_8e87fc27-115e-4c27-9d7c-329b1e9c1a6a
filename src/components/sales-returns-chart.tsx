import { Suspense } from "react";
import type { DateRange } from "react-day-picker";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { api } from "~/trpc/react";
import { IncompleteData } from "~/components/ui/incomplete-data";
import { LoadingSpinner } from "~/components/ui/loading-spinner";
import { format } from "date-fns";

interface SalesReturnsChartProps {
  dateRange: DateRange | undefined;
  selectedStores: string[];
  selectedPublishers: string[];
}

function SalesReturnsChartData({
  dateRange,
  selectedStores,
  selectedPublishers,
}: SalesReturnsChartProps) {
  const [data] = api.sales.getSalesByMonth.useSuspenseQuery({
    stores: selectedStores,
    publishers: selectedPublishers,
    dateRange,
  });

  const sortedData = data
    .toSorted((a, b) => a.month.localeCompare(b.month))
    .map((month) => ({
      month: new Date(month.month),
      grossSales: month.grossRevenue,
      returns: month.returnRevenue,
    }));

  const allInCurrentYear =
    sortedData[0]?.month?.getFullYear() ===
      sortedData[sortedData.length - 1]?.month?.getFullYear() &&
    sortedData[0]?.month?.getFullYear() === new Date().getFullYear();

  const getXAxisLabel = (index: number) => {
    return allInCurrentYear
      ? format(sortedData[index]!.month, "MMM")
      : format(sortedData[index]!.month, "MMM yy");
  };

  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={sortedData}
          margin={{
            top: 5,
            right: 10,
            left: 10,
            bottom: 0,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            tickLine={false}
            axisLine={false}
            padding={{ left: 10, right: 10 }}
            tickFormatter={getXAxisLabel}
          />
          <YAxis
            tickFormatter={(value: string) => `$${value.toLocaleString()}`}
            tickLine={false}
            axisLine={false}
            tickCount={6}
          />
          <Tooltip
            content={({ active, payload }) => {
              if (!active || !payload?.length) {
                return null;
              }
              return (
                <div className="bg-background rounded-lg border p-2 shadow-sm">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex flex-col">
                      <span className="text-muted-foreground text-[0.70rem] uppercase">
                        Sales
                      </span>
                      <span className="text-muted-foreground font-bold">
                        ${payload[0]?.value?.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-muted-foreground text-[0.70rem] uppercase">
                        Returns
                      </span>
                      <span className="text-muted-foreground font-bold">
                        ${payload[1]?.value?.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              );
            }}
          />
          <Line
            type="monotone"
            dataKey="grossSales"
            name="Gross Sales"
            stroke="#0ea5e9"
            strokeWidth={2}
            activeDot={{ r: 6, style: { fill: "#0ea5e9" } }}
          />
          <Line
            type="monotone"
            dataKey="returns"
            name="Returns"
            stroke="#ef4444"
            strokeWidth={2}
            activeDot={{ r: 6, style: { fill: "#ef4444" } }}
          />
          <Legend />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

export function SalesReturnsChart({
  dateRange,
  selectedStores,
  selectedPublishers,
}: SalesReturnsChartProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Gross Sales & Returns</CardTitle>
          <CardDescription>Monthly gross sales and returns</CardDescription>
        </div>
        <IncompleteData />
      </CardHeader>
      <CardContent>
        <Suspense
          fallback={
            <div className="text-muted-foreground flex h-[300px] items-center justify-center">
              <LoadingSpinner />
            </div>
          }
        >
          <SalesReturnsChartData
            dateRange={dateRange}
            selectedStores={selectedStores}
            selectedPublishers={selectedPublishers}
          />
        </Suspense>
      </CardContent>
    </Card>
  );
}
