import { useEffect, useState } from "react";
import type { newStoreRow } from "~/lib/ingest/common";
import { useForm } from "react-hook-form";
import { type storesInsert, storesInsertValidator } from "~/server/db/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { formatAddress, removeBadWords, toTitleCase } from "~/lib/utils";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Button } from "~/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { undefined } from "zod/v4";
import {
  type getAllReadStores,
  type getPublisher,
  type getStoreTypes,
} from "~/server/queries";
import {
  getStores,
  insert<PERSON><PERSON><PERSON>,
  insertNewStore,
  insertRecords,
} from "~/lib/ingest/insert";
import { type DateRange } from "react-day-picker";
import Fuse from "fuse.js";
import { RadioGroup } from "~/components/ui/radio-group";
import { Checkbox } from "~/components/ui/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { Check, ChevronsUpDown } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from "~/components/ui/command";
import { AddressAutofill } from "~/components/address-autocomplete";
import { useMapsLibrary } from "@vis.gl/react-google-maps";

interface NewStoreFormProps {
  newStores: newStoreRow[];
  selectedPublisher: getPublisher;
  storeTypes: getStoreTypes;
  readStores: getAllReadStores;
  period: DateRange;
  setNewStores: (stores: newStoreRow[] | null) => void;
}

export function NewStoreForm({
  newStores,
  selectedPublisher,
  storeTypes,
  readStores,
  period,
  setNewStores,
}: NewStoreFormProps) {
  const newStore = newStores[0]!;

  const [selectedExistingStoreId, setSelectedExistingStoreId] = useState<
    number | null
  >(null);
  const [storeSearchTerm, setStoreSearchTerm] = useState("");
  const [storeCommandOpen, setStoreCommandOpen] = useState(false);

  const [existingStoreSelectValue, setExistingStoreSelectValue] = useState("");
  const [closeStores, setCloseStores] = useState<getAllReadStores>([]);
  const [selectedCloseStore, setSelectedCloseStore] = useState<number | null>(
    null,
  );

  const [validatedAddress, setValidatedAddress] = useState<
    Record<string, string>
  >({});

  const [_, setStores] = useState<getAllReadStores>(readStores);

  const nextNewStore = async () => {
    setStores(await getStores());
    if (newStores) {
      const nextStore = newStores.slice(1);
      void validateAddress(nextStore[0]?.address);
      setNewStores(nextStore);
    }
  };

  const newStoreForm = useForm<storesInsert>({
    resolver: zodResolver(storesInsertValidator),
    defaultValues: {
      name: removeBadWords(toTitleCase(newStore.identifier ?? "")),
      branchName: removeBadWords(toTitleCase(newStore.identifier2 ?? "")),
      typeId: 0,
      contactName: "",
      contactEmail: "",
      contactPhone: "",
      repId: 0,
      streetNumber: "",
      route: "",
      subpremise: "",
      city: "",
      province: "",
      country: "",
      postalCode: "",
    },
  });

  /* eslint-disable */ // beta channel
  // @ts-ignore
  const VerifyLib = useMapsLibrary("addressValidation");
  // @ts-ignore
  const validateAddress = async (address: string | undefined) => {
    if (!VerifyLib || !address) return;
    // @ts-ignore
    const validated = await VerifyLib.AddressValidation.fetchAddressValidation({
      address: {
        addressLines: [address],
      },
    });
    if (validated.verdict.possibleNextAction === "ACCEPT") {
      setValidatedAddress(formatAddress(validated.address.components));
    }
  };
  /* eslint-enable */

  useEffect(() => {
    newStoreForm.reset();
    newStoreForm.setValue(
      "name",
      removeBadWords(toTitleCase(newStore.identifier)) ?? "",
    );
    newStoreForm.setValue(
      "branchName",
      removeBadWords(toTitleCase(newStore.identifier2)) ?? "",
    );

    setSelectedExistingStoreId(null);
    setExistingStoreSelectValue("");
    setSelectedCloseStore(null);

    const fuse = new Fuse(readStores, {
      keys: ["name"],
      threshold: 0.95,
    });
    const results = fuse.search(newStore.identifier ?? "");
    setCloseStores(results.map((r) => r.item));
  }, [newStore, newStoreForm, readStores]);

  useEffect(() => {
    newStoreForm.setValue("streetNumber", validatedAddress.street_number ?? "");
    newStoreForm.setValue("route", validatedAddress.route ?? "");
    newStoreForm.setValue("subpremise", validatedAddress.subpremise ?? "");
    newStoreForm.setValue(
      "city",
      validatedAddress.locality ?? validatedAddress.sublocality_level_1 ?? "",
    );
    newStoreForm.setValue(
      "province",
      validatedAddress.administrative_area_level_1 ?? "",
    );
    newStoreForm.setValue("country", validatedAddress.country ?? "");
    newStoreForm.setValue("postalCode", validatedAddress.postal_code ?? "");
  }, [validatedAddress, newStoreForm]);

  const onNewStoreSubmit = async (values: storesInsert) => {
    const newId = await insertNewStore(values);
    if (!newId) return;
    console.log(`Created new store ${values.name}`);

    await insertAlias({
      publisherId: selectedPublisher!.id,
      storeId: newId,
      publisherStoreId: newStore.publisherStoreId,
      identifier: newStore.identifier,
      identifier2: newStore.identifier2,
    });
    console.log(
      `Created new ${selectedPublisher?.name} alias for ${values.name}`,
    );

    await insertRecords(
      [
        {
          storeId: newId,
          publisherId: selectedPublisher!.id,
          publisherStoreId: newStore.publisherStoreId,
          identifier: values.name,
          netRevenue: newStore.netRevenue,
          netQuantity: newStore.netQuantity,
          returnRevenue: newStore.returnRevenue,
          returnQuantity: newStore.returnQuantity,
          grossRevenue: newStore.grossRevenue,
          grossQuantity: newStore.grossQuantity,
        },
      ],
      period,
    );
    console.log("Uploaded one record");

    await nextNewStore();
  };

  const onAliasSubmit = async () => {
    if (!existingStoreSelectValue && !selectedCloseStore) return;

    const id = selectedCloseStore ?? Number(existingStoreSelectValue);

    const existingStore = readStores.find((s) => s.id === id);
    if (!existingStore) return;

    await insertAlias({
      publisherId: selectedPublisher!.id,
      storeId: existingStore.id,
      publisherStoreId: newStore.publisherStoreId,
      identifier: newStore.identifier,
    });
    console.log(
      `Created new ${selectedPublisher?.name} alias for ${existingStore.name}`,
    );

    await insertRecords(
      [
        {
          storeId: existingStore.id,
          publisherId: selectedPublisher!.id,
          publisherStoreId: newStore.publisherStoreId,
          identifier: existingStore.name,
          netRevenue: newStore.netRevenue,
          netQuantity: newStore.netQuantity,
          returnRevenue: newStore.returnRevenue,
          returnQuantity: newStore.returnQuantity,
          grossRevenue: newStore.grossRevenue,
          grossQuantity: newStore.grossQuantity,
        },
      ],
      period,
    );
    console.log("Uploaded one record");

    await nextNewStore();
  };

  const onCheckboxChange = (storeId: number, checked: boolean) => {
    if (checked) {
      setSelectedCloseStore(storeId);
      setExistingStoreSelectValue("");
    } else {
      if (selectedCloseStore === storeId) {
        setSelectedCloseStore(null);
      }
    }
  };

  const selectedStoreName =
    readStores.find((store) => store.id === selectedExistingStoreId)?.name ??
    "";

  return (
    <>
      <div className="space-y-4">
        <p className="w-96 text-center text-lg">
          Create new stores / aliases
          <br />
          {newStores.length} to go
        </p>
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <h2 className="text-center text-2xl font-bold">
            {newStore.identifier}
          </h2>

          <Label>Existing Stores</Label>

          <RadioGroup>
            {closeStores.length > 0 &&
              closeStores.slice(0, 3).map((store) => (
                <div key={store.id}>
                  <Checkbox
                    id={store.id.toString()}
                    checked={selectedCloseStore === store.id}
                    onCheckedChange={(checked) =>
                      onCheckboxChange(store.id, checked as boolean)
                    }
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor={store.id.toString()}
                    className="border-muted bg-popover text-accent-foreground peer-data-[state=checked]:bg-chart-3 flex flex-col items-center justify-between rounded-md border-2 p-4 hover:brightness-90"
                  >
                    {store.name}
                  </Label>
                </div>
              ))}
          </RadioGroup>

          <Popover open={storeCommandOpen} onOpenChange={setStoreCommandOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={storeCommandOpen}
                className="w-full justify-between"
              >
                {selectedExistingStoreId
                  ? selectedStoreName
                  : "Select a store..."}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0" align="start">
              <Command>
                <CommandInput
                  placeholder="Search stores..."
                  value={storeSearchTerm}
                  onValueChange={setStoreSearchTerm}
                />
                <CommandList className="max-h-[300px] overflow-auto">
                  <CommandEmpty>No stores found.</CommandEmpty>
                  {readStores
                    .toSorted((a, b) => a.name.localeCompare(b.name))
                    .filter((store) =>
                      store.name
                        .toLowerCase()
                        .includes(storeSearchTerm.toLowerCase()),
                    )
                    .slice(0, 100)
                    .map((store) => (
                      <CommandItem
                        key={store.id}
                        value={store.name}
                        onSelect={() => {
                          setSelectedExistingStoreId(store.id);
                          setSelectedCloseStore(null);
                          setStoreCommandOpen(false); // Close after selection
                        }}
                      >
                        <Check
                          className={`mr-2 h-4 w-4 ${
                            selectedExistingStoreId === store.id
                              ? "opacity-100"
                              : "opacity-0"
                          }`}
                        />
                        {store.name}
                      </CommandItem>
                    ))}
                  {readStores.length > 100 && (
                    <div className="text-muted-foreground px-2 py-2 text-xs">
                      Showing 100 of {readStores.length} stores. Refine your
                      search to see more.
                    </div>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          <Button type="submit" onClick={onAliasSubmit}>
            Add alias
          </Button>
        </div>

        <hr />

        <p>
          Publisher: {selectedPublisher?.name}
          <br />
          Publisher Id: {newStore.publisherStoreId}
        </p>

        <Form {...newStoreForm}>
          <form
            onSubmit={newStoreForm.handleSubmit(onNewStoreSubmit)}
            autoComplete="aaa"
          >
            <FormField
              control={newStoreForm.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input {...field} autoComplete="disabled" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={newStoreForm.control}
              name="branchName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Branch Name</FormLabel>
                  <FormControl>
                    <Input type="text" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <AddressAutofill onValidate={setValidatedAddress} />

            <div className="space-y-2">
              <Label className="text-sm font-medium">Address</Label>
              <div className="grid grid-cols-2 gap-2">
                <FormField
                  control={newStoreForm.control}
                  name="streetNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Street #</FormLabel>
                      <FormControl>
                        <Input
                          disabled
                          type="text"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={newStoreForm.control}
                  name="route"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Street Name</FormLabel>
                      <FormControl>
                        <Input
                          disabled
                          type="text"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={newStoreForm.control}
                  name="subpremise"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Unit/Suite</FormLabel>
                      <FormControl>
                        <Input type="text" {...field} className="h-8" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={newStoreForm.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">City</FormLabel>
                      <FormControl>
                        <Input
                          disabled
                          type="text"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={newStoreForm.control}
                  name="province"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Province/State</FormLabel>
                      <FormControl>
                        <Input
                          disabled
                          type="text"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={newStoreForm.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Country</FormLabel>
                      <FormControl>
                        <Input
                          disabled
                          type="text"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={newStoreForm.control}
                name="postalCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs">Postal Code</FormLabel>
                    <FormControl>
                      <Input
                        disabled
                        type="text"
                        {...field}
                        className="h-8 max-w-32"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={newStoreForm.control}
              name="typeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Type of Store</FormLabel>
                  <FormControl>
                    <Select
                      {...field}
                      onValueChange={(value) => {
                        const numericValue = value
                          ? Number.parseInt(value, 10)
                          : undefined;
                        field.onChange(numericValue);
                      }}
                      value={field.value?.toString() ?? ""}
                    >
                      <SelectTrigger className={"size-96"}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {storeTypes
                          .sort((a, b) => a.id - b.id)
                          .map((type) => (
                            <SelectItem
                              key={type.id}
                              value={type.id.toString()}
                            >
                              {type.id === 0 ? "-unset-" : type.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={newStoreForm.control}
              name="contactName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contact Name</FormLabel>
                  <FormControl>
                    <Input type="text" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={newStoreForm.control}
              name="contactPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contact Phone</FormLabel>
                  <FormControl>
                    <Input type="text" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={newStoreForm.control}
              name="contactEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contact Email</FormLabel>
                  <FormControl>
                    <Input type="text" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full">
              Create Store
            </Button>
          </form>
        </Form>
      </div>
    </>
  );
}
