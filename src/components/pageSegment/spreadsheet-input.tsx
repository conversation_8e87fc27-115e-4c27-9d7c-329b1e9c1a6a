import { type ChangeEvent, useCallback } from "react";
import { Input } from "~/components/ui/input";
import * as XLSX from "xlsx";

interface SpreadsheetInputProps {
  setWorkbook: (workbook: XLSX.WorkBook | null) => void;
  setError: (error: string | null) => void;
  setSpreadsheet: (spreadsheet: File | null) => void;
  startTransition: (callback: () => void) => void;
}

export function SpreadsheetInput(props: SpreadsheetInputProps) {
  const { setWorkbook, setError, setSpreadsheet, startTransition } = props;

  const processExcelFile = useCallback(
    async (file: File) => {
      try {
        console.log(file);
        const w = XLSX.read(await file.arrayBuffer());
        setWorkbook(w);
        setError(null);
      } catch (err) {
        console.log(err);
        setError(
          "Error processing Excel file. Please ensure it's a valid .xlsx file.",
        );
        setWorkbook(null);
      }
    },
    [setWorkbook, setError],
  );

  const handleSpreadsheetChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] ?? null;
    setSpreadsheet(file);

    if (file) {
      startTransition(() => {
        void processExcelFile(file);
      });
    }
  };

  return (
    <>
      <Input
        id="spreadsheet-input"
        type="file"
        className="w-96"
        onChange={handleSpreadsheetChange}
        accept=".xlsx,.xls"
      />
    </>
  );
}
