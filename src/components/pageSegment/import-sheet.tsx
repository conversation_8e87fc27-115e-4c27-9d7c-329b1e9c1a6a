"use client";
import { useState, useTransition } from "react";
import type { DateRange } from "react-day-picker";
import type {
  getAllPublishers,
  getAllReadStores,
  getPublisher,
  getStoreTypes,
} from "~/server/queries";
import { type reportTypes } from "~/server/db/schema";
import type { InferSelectModel } from "drizzle-orm";
import type * as XLSX from "xlsx";
import { type newStoreRow } from "~/lib/ingest/common";
import { NewStoreForm } from "~/components/pageSegment/new-store-form";
import { SpreadsheetInput } from "~/components/pageSegment/spreadsheet-input";
import { SheetOptions } from "~/components/pageSegment/sheet-options";
import { APIProvider } from "@vis.gl/react-google-maps";
import { env } from "~/env";

interface SheetOptionsProps {
  publishers: getAllPublishers;
  readStores: getAllReadStores;
  storeTypes: getStoreTypes;
}

export function ImportSheet({
  publishers,
  readStores,
  storeTypes,
}: SheetOptionsProps) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [spreadsheet, setSpreadsheet] = useState<File | null>(null);
  const [selectedPublisher, setSelectedPublisher] = useState<getPublisher>();
  const [selectedReportType, setSelectedReportType] = useState<
    InferSelectModel<typeof reportTypes> | undefined
  >();
  const [workbook, setWorkbook] = useState<XLSX.WorkBook | null>(null);
  const [worksheet, setWorksheet] = useState<XLSX.WorkSheet | undefined>();
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);
  const [newStores, setNewStores] = useState<newStoreRow[] | null>(null);

  if (newStores?.[0] && dateRange) {
    return (
      <APIProvider apiKey={env.NEXT_PUBLIC_GOOGLE_API_KEY} version="beta">
        <NewStoreForm
          newStores={newStores}
          selectedPublisher={selectedPublisher}
          storeTypes={storeTypes}
          readStores={readStores}
          period={dateRange}
          setNewStores={setNewStores}
        />
      </APIProvider>
    );
  } else if (spreadsheet) {
    const sheetOptionsProps = {
      publishers,
      dateRange,
      setDateRange,
      error,
      isPending,
      selectedPublisher,
      selectedReportType,
      setNewStores,
      setSelectedPublisher,
      setSelectedReportType,
      setWorksheet,
      spreadsheet,
      workbook,
      worksheet,
    };
    return <SheetOptions {...sheetOptionsProps}></SheetOptions>;
  } else {
    return (
      <SpreadsheetInput
        setSpreadsheet={setSpreadsheet}
        setWorkbook={setWorkbook}
        setError={setError}
        startTransition={startTransition}
      />
    );
  }
}
