import type React from "react";
import { But<PERSON> } from "~/components/ui/button";
import { UploadIcon } from "lucide-react";
import Link from "next/link";

interface DashboardHeaderProps {
  heading: string;
  text?: string;
  children?: React.ReactNode;
}

export function DashboardHeader({
  heading,
  text,
  children,
}: DashboardHeaderProps) {
  return (
    <div className="flex items-center justify-between px-2">
      <div className="grid gap-1">
        <h1 className="font-heading text-3xl md:text-4xl">{heading}</h1>
        {text && <p className="text-muted-foreground text-lg">{text}</p>}
      </div>
      <Link href="/upload">
        <Button variant="outline" size="sm">
          <UploadIcon className="h-4 w-4" />
          Upload
        </Button>
      </Link>
      {children}
    </div>
  );
}
