import { Suspense } from "react";
import type { DateRange } from "react-day-picker";
import { format } from "date-fns";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { api } from "~/trpc/react";
import { IncompleteData } from "~/components/ui/incomplete-data";
import { LoadingSpinner } from "~/components/ui/loading-spinner";

interface QuantityChartProps {
  dateRange: DateRange | undefined;
  selectedStores: string[];
  selectedPublishers: string[];
}

function QuantityChartData({
  dateRange,
  selectedStores,
  selectedPublishers,
}: QuantityChartProps) {
  const [data] = api.sales.getSalesByMonth.useSuspenseQuery({
    stores: selectedStores,
    publishers: selectedPublishers,
    dateRange,
  });

  const sortedData = data
    .toSorted((a, b) => a.month.localeCompare(b.month))
    .map((month) => ({
      month: new Date(month.month),
      grossQuantity: month.grossQuantity,
      returnQuantity: month.returnQuantity,
      netQuantity: month.netQuantity,
    }));

  const allInCurrentYear =
    sortedData[0]?.month?.getFullYear() ===
      sortedData[sortedData.length - 1]?.month?.getFullYear() &&
    sortedData[0]?.month?.getFullYear() === new Date().getFullYear();

  const getXAxisLabel = (index: number) => {
    return allInCurrentYear
      ? format(sortedData[index]!.month, "MMM")
      : format(sortedData[index]!.month, "MMM yy");
  };

  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={sortedData}
          margin={{
            top: 5,
            right: 10,
            left: 10,
            bottom: 0,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            tickLine={false}
            axisLine={false}
            padding={{ left: 10, right: 10 }}
            tickFormatter={getXAxisLabel}
          />
          <YAxis tickLine={false} axisLine={false} tickCount={6} />
          <Tooltip
            content={({ active, payload }) => {
              if (active && payload?.length) {
                return (
                  <div className="bg-background rounded-lg border p-2 shadow-sm">
                    <div className="grid grid-cols-3 gap-2">
                      <div className="flex flex-col">
                        <span className="text-muted-foreground text-[0.70rem] uppercase">
                          Sale Qty
                        </span>
                        <span className="text-muted-foreground font-bold">
                          {payload[0]?.value?.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground text-[0.70rem] uppercase">
                          Return Qty
                        </span>
                        <span className="text-muted-foreground font-bold">
                          {payload[1]?.value?.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground text-[0.70rem] uppercase">
                          Net Qty
                        </span>
                        <span className="text-muted-foreground font-bold">
                          {payload[2]?.value?.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              }
              return null;
            }}
          />
          <Line
            type="monotone"
            dataKey="netQuantity"
            name="Net Quantity"
            stroke="#10b981"
            strokeWidth={2}
            activeDot={{ r: 6, style: { fill: "#10b981" } }}
          />
          <Line
            type="monotone"
            dataKey="grossQuantity"
            name="Gross Quantity"
            stroke="#0ea5e9"
            strokeWidth={2}
            activeDot={{ r: 6, style: { fill: "#0ea5e9" } }}
          />
          <Line
            type="monotone"
            dataKey="returnQuantity"
            name="Return Quantity"
            stroke="#ef4444"
            strokeWidth={2}
            activeDot={{ r: 6, style: { fill: "#ef4444" } }}
          />
          <Legend />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

export function QuantityChart({
  dateRange,
  selectedStores,
  selectedPublishers,
}: QuantityChartProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Quantity Metrics</CardTitle>
          <CardDescription>Net, gross, and return quantities</CardDescription>
        </div>
        <IncompleteData
          text={"Gross and return quantities do not include PGC"}
        />
      </CardHeader>
      <CardContent>
        <Suspense
          fallback={
            <div className="h-[300px]">
              <div className="text-muted-foreground flex h-full items-center justify-center">
                <LoadingSpinner />
              </div>
            </div>
          }
        >
          <QuantityChartData
            dateRange={dateRange}
            selectedStores={selectedStores}
            selectedPublishers={selectedPublishers}
          />
        </Suspense>
      </CardContent>
    </Card>
  );
}
