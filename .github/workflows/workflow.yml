on:
  schedule: 
    # runs once a week on sunday
    - cron: "55 23 * * 0"
    #- cron: "*/1 * * * *"
    
jobs:
  # This workflow contains a single job called "traffic"
  traffic:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
    # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
    - uses: actions/checkout@v2
      with:
        ref: "traffic"
    
    # Calculates traffic and clones and stores in CSV file
    - name: GitHub traffic 
      uses: sangonzal/repository-traffic-action@v.0.1.6
      env:
        TRAFFIC_ACTION_TOKEN: ${{ secrets.TRAFFIC_ACTION_TOKEN }} 
     
    # Commits files to repository
    - name: Commit changes
      uses: EndBug/add-and-commit@v4
      with:
        author_name: <PERSON>
        message: "GitHub traffic"
        add: "./traffic/*"
        ref: "traffic"  # commits to branch "traffic"
