diff --git a/node_modules/@vis.gl/react-google-maps/src/hooks/use-maps-library.ts b/node_modules/@vis.gl/react-google-maps/src/hooks/use-maps-library.ts
index db46e00..321a1e0 100644
--- a/node_modules/@vis.gl/react-google-maps/src/hooks/use-maps-library.ts
+++ b/node_modules/@vis.gl/react-google-maps/src/hooks/use-maps-library.ts
@@ -16,6 +16,8 @@ interface ApiLibraries {
   journeySharing: google.maps.JourneySharingLibrary;
   drawing: google.maps.DrawingLibrary;
   visualization: google.maps.VisualizationLibrary;
+  // @ts-ignore
+  addressValidation: google.maps.addressValidation;
 }
 
 export function useMapsLibrary<
