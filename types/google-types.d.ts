declare namespace google {
  namespace maps {
    namespace addressValidation {
      /**
       * Notice: Available only in the v=beta channel.
       *
       * Static class for accessing the AddressValidation APIs.
       *
       * Access by calling `const {AddressValidation} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      class AddressValidation {
        /**
         * Validates an address. See https://developers.google.com/maps/documentation/javascript/address-validation/validate-address.
         */
        static fetchAddressValidation(
          request: AddressValidationRequest,
        ): Promise<AddressValidation>;
        /**
         * Information about the address itself as opposed to the geocode.
         */
        address?: Address;
        /**
         * Information about the location and place that the address geocoded to.
         */
        geocode?: Geocode;
        /**
         * Other information relevant to deliverability. metadata is not guaranteed to be fully populated for every address sent to the Address Validation API.
         */
        metadata?: AddressMetadata;
        /**
         * The UUID that identifies this response. If the address needs to be re-validated, this UUID must accompany the new request.
         */
        responseId?: string;
        /**
         * Extra deliverability flags provided by USPS. Only provided in region US and PR.
         */
        uspsData?: USPSData;
        /**
         * Overall verdict flags
         */
        verdict?: Verdict;
        /**
         * Converts the AddressValidation class to a JSON object with the same properties.
         */
        toJSON(): Record<string, unknown>;
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * Request interface for AddressValidation.fetchAddressValidation.
       */
      interface AddressValidationRequest {
        /**
         * The address being validated. Unformatted addresses should be submitted via PostalAddress.addressLines.
         */
        address: google.maps.places.PostalAddressLiteral;
        /**
         * If using a PlaceAutocompleteElement, include it here to link the AddressValidation API calls with the autocomplete session token.
         */
        placeAutocompleteElement?: google.maps.places.PlaceAutocompleteElement;
        /**
         * This field must not be set for the first address validation request. If more requests are necessary to fully validate a single address (for example if the changes the user makes after the initial validation need to be re-validated), then each followup request must populate this field with the AddressValidation.responseId from the very first response in the validation sequence.
         */
        previousResponseId?: string;
        /**
         * A token which identifies an Autocomplete session for billing purposes.
         */
        sessionToken?: google.maps.places.AutocompleteSessionToken;
        /**
         * Enables USPS CASS compatible mode. This affects only the AddressValidation.uspsData field of AddressValidation. Note: for USPS CASS enabled requests for addresses in Puerto Rico, a PostalAddress.regionCode of the address must be provided as "PR", or an PostalAddress.administrativeArea of the address must be provided as "Puerto Rico" (case-insensitive) or "PR".
         */
        uspsCASSEnabled?: boolean;
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * Details of the post-processed address. Post-processing includes correcting misspelled parts of the address, replacing incorrect parts, and inferring missing parts.
       *
       * Access by calling `const {Address} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      class Address {
        /**
         * The individual address components of the formatted and corrected address, along with validation information. This provides information on the validation status of the individual components.
         */
        components: AddressComponent[];
        /**
         * The post-processed address, formatted as a single-line address following the address-formatting rules of the region where the address is located.
         */
        formattedAddress?: string;
        /**
         * The types of components that were expected to be present in a correctly formatted mailing address but were not found in the input AND could not be inferred. Components of this type are not present in formatted_address, postal_address, or address_components. An example might be ['street_number', 'route'] for an input like "Boulder, Colorado, 80301, USA". The list of possible types can be found here.
         */
        missingComponentTypes: string[];
        /**
         * The post-processed address represented as a postal address.
         */
        postalAddress?: google.maps.places.PostalAddress;
        /**
         * The types of the components that are present in the address_components but could not be confirmed to be correct. This field is provided for the sake of convenience: its contents are equivalent to iterating through the address_components to find the types of all the components where the AddressComponent.confirmationLevel is not ConfirmationLevel.CONFIRMED or the AddressComponent.inferred flag is not set to true. The list of possible types can be found here.
         */
        unconfirmedComponentTypes: string[];
        /**
         * Any tokens in the input that could not be resolved. This might be an input that was not recognized as a valid part of an address (for example in an input like "123235253253 Main St, San Francisco, CA, 94105", the unresolved tokens may look like ["123235253253"] since that does not look like a valid street number.
         */
        unresolvedTokens: string[];
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * Represents a single component of an address (ex. street name, city).
       *
       * Access by calling `const {AddressComponent} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      class AddressComponent {
        /**
         * The component name text. For example, "5th Avenue" for a street name or "1253" for a street number,
         */
        componentName?: string;
        /**
         * The BCP-47 language code. This will not be present if the component name is not associated with a language, such as a street number.
         */
        componentNameLanguageCode?: string;
        /**
         * The type of the address component. See Table 2: Additional types returned by the Places service for a list of possible types.
         */
        componentType?: string;
        /**
         * Indicates the level of certainty that the component is correct.
         */
        confirmationLevel?: ConfirmationLevel;
        /**
         * If true, this component was not part of the input, but was inferred for the address location. Including this component is recommended for a complete address.
         */
        inferred: boolean;
        /**
         * Indicates the name of the component was replaced with a completely different one. For example, replacing a wrong postal code being with one that is correct for the address. This is not a cosmetic change; the input component has been changed to a different one.
         */
        replaced: boolean;
        /**
         * Indicates a correction to a misspelling in the component name. The API does not always flag changes from one spelling variant to another, such as "centre" to "center".
         */
        spellCorrected: boolean;
        /**
         * If true, this component is not expected to be present in a postal address for the given region. It has been retained only because it was part of the input.
         */
        unexpected: boolean;
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * The metadata for the address. AddressMetadata is not guaranteed to be fully populated for every address sent to the Address Validation API.
       *
       * Access by calling `const {AddressMetadata} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      class AddressMetadata {
        business: boolean;
        poBox: boolean;
        residential: boolean;
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * The different possible values indicating the level of certainty that the component is correct.
       *
       * Access by calling `const {ConfirmationLevel} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      enum ConfirmationLevel {
        CONFIRMED = "CONFIRMED",
        UNCONFIRMED_AND_SUSPICIOUS = "UNCONFIRMED_AND_SUSPICIOUS",
        UNCONFIRMED_BUT_PLAUSIBLE = "UNCONFIRMED_BUT_PLAUSIBLE",
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * Contains information about the place the input was geocoded to.
       *
       * Access by calling `const {Geocode} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      class Geocode {
        /**
         * The bounds of the geocoded place.
         */
        bounds?: google.maps.LatLngBounds;
        /**
         * The size of the geocoded place, in meters. This is another measure of the coarseness of the geocoded location, but in physical size rather than in semantic meaning.
         */
        featureSizeMeters?: number;
        /**
         * The geocoded location of the input.
         */
        location?: google.maps.LatLngAltitude;
        /**
         * The Place ID of the geocoded place. Using Place is preferred over using addresses, latitude/longitude coordinates, or plus codes. Using coordinates for routing or calculating driving directions will always result in the point being snapped to the road nearest to those coordinates. This may not be a road that will quickly or safely lead to the destination and may not be near an access point to the property. Additionally, when a location is reverse geocoded, there is no guarantee that the returned address will match the original.
         */
        placeId?: string;
        /**
         * The type(s) of place that the input geocoded to. For example, ['locality', 'political']. The full list of types can be found in the Geocoding API documentation.
         */
        placeTypes: string[];
        /**
         * The plus code corresponding to the location.
         */
        plusCode?: google.maps.places.PlusCode;
        /**
         * Returns a Place representation of this Geocode. To get full place details, a call to place.fetchFields() should be made.
         */
        fetchPlace(): void;
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * The various granularities that an address or a geocode can have. When used to indicate granularity for an address, these values indicate with how fine a granularity the address identifies a mailing destination. For example, an address such as "123 Main Street, Redwood City, CA, 94061" identifies a PREMISE while something like "Redwood City, CA, 94061" identifies a LOCALITY. However, if we are unable to find a geocode for "123 Main Street" in Redwood City, the geocode returned might be of LOCALITY granularity even though the address is more granular.
       *
       * Access by calling `const {Granularity} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      enum Granularity {
        /**
         * The address or geocode indicates a block. Only used in regions which have block-level addressing, such as Japan.
         */
        BLOCK = "BLOCK",
        /**
         * All other granularities, which are bucketed together since they are not deliverable.
         */
        OTHER = "OTHER",
        /**
         * Building-level result.
         */
        PREMISE = "PREMISE",
        /**
         * A geocode that approximates the building-level location of the address.
         */
        PREMISE_PROXIMITY = "PREMISE_PROXIMITY",
        /**
         * The geocode or address is granular to route, such as a street, road, or highway.
         */
        ROUTE = "ROUTE",
        /**
         * Below-building level result, such as an apartment.
         */
        SUB_PREMISE = "SUB_PREMISE",
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * Offers an interpretive summary of the API response, intended to assist in determining a potential subsequent action to take. This field is derived from other fields in the API response and should not be considered as a guarantee of address accuracy or deliverability.
       *
       * Access by calling `const {PossibleNextAction} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      enum PossibleNextAction {
        /**
         * The API response does not contain signals that warrant one of the other PossibleNextAction values. You might consider using the post-processed address without further prompting your customer, though this does not guarantee the address is valid, and the address might still contain corrections. It is your responsibility to determine if and how to prompt your customer, depending on your own risk assessment.
         */
        ACCEPT = "ACCEPT",
        /**
         * One or more fields of the API response indicate potential minor issues with the post-processed address, for example the postal_code address component was replaced. Prompting your customer to review the address could help improve the quality of the address.
         */
        CONFIRM = "CONFIRM",
        /**
         * The API response indicates the post-processed address might be missing a subpremises. Prompting your customer to review the address and consider adding a unit number could help improve the quality of the address. The post-processed address might also have other minor issues. Note: this enum value can only be returned for US addresses.
         */
        CONFIRM_ADD_SUBPREMISES = "CONFIRM_ADD_SUBPREMISES",
        /**
         * One or more fields of the API response indicate a potential issue with the post-processed address, for example the verdict.validation_granularity is OTHER. Prompting your customer to edit the address could help improve the quality of the address.
         */
        FIX = "FIX",
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * USPS representation of a US address.
       *
       * Access by calling `const {USPSAddress} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      class USPSAddress {
        /**
         * The city name.
         */
        city?: string;
        /**
         * The address line containing the city, state, and zip code.
         */
        cityStateZipAddressLine?: string;
        /**
         * The name of the firm.
         */
        firm?: string;
        /**
         * The first line of the address.
         */
        firstAddressLine?: string;
        /**
         * The second line of the address.
         */
        secondAddressLine?: string;
        /**
         * The 2-letter state code.
         */
        state?: string;
        /**
         * The Puerto Rican urbanization name.
         */
        urbanization?: string;
        /**
         * The Postal code, e.g. "10009".
         */
        zipCode?: string;
        /**
         * The 4-digit postal code extension, e.g. "5023".
         */
        zipCodeExtension?: string;
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * The USPS data for the address. USPSData is not guaranteed to be fully populated for every US or PR address sent to the Address Validation API. It's recommended to integrate the backup address fields in the response if you utilize uspsData as the primary part of the response.
       *
       * Access by calling `const {USPSData} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      class USPSData {
        /**
         * Abbreviated city.
         */
        abbreviatedCity?: string;
        /**
         * Type of the address record that matches the input address.
         */
        addressRecordType?: string;
        /**
         * The carrier route code. A four character code consisting of a one letter prefix and a three digit route designator.
         */
        carrierRoute?: string;
        /**
         * Carrier route rate sort indicator.
         */
        carrierRouteIndicator?: string;
        /**
         * Indicator that the request has been CASS processed.
         */
        cassProcessed: boolean;
        /**
         * County name.
         */
        county?: string;
        /**
         * The delivery point check digit. This number is added to the end of the delivery_point_barcode for mechanically scanned mail. Adding all the digits of the delivery_point_barcode, delivery_point_check_digit, postal code, and ZIP+4 together should yield a number divisible by 10.
         */
        deliveryPointCheckDigit?: string;
        /**
         * The 2-digit delivery point code.
         */
        deliveryPointCode?: string;
        /**
         * Indicates if the address is a CMRA (Commercial Mail Receiving Agency)--a private business receiving mail for clients. Returns a single character.
         */
        dpvCMRA?: string;
        /**
         * The possible values for DPV confirmation. Returns a single character or returns no value.
         */
        dpvConfirmation?: string;
        /**
         * Flag indicates addresses where USPS cannot knock on a door to deliver mail. Returns a single character.
         */
        dpvDoorNotAccessible?: string;
        /**
         * Flag indicates mail is delivered to a single receptable at a site. Returns a single character.
         */
        dpvDrop?: string;
        /**
         * Indicates that more than one DPV return code is valid for the address. Returns a single character.
         */
        dpvEnhancedDeliveryCode?: string;
        /**
         * The footnotes from delivery point validation. Multiple footnotes may be strung together in the same string.
         */
        dpvFootnote?: string;
        /**
         * Flag indicates mail delivery is not performed every day of the week. Returns a single character.
         */
        dpvNonDeliveryDays?: string;
        /**
         * Integer identifying non-delivery days. It can be interrogated using bit flags: 0x40 – Sunday is a non-delivery day 0x20 – Monday is a non-delivery day 0x10 – Tuesday is a non-delivery day 0x08 – Wednesday is a non-delivery day 0x04 – Thursday is a non-delivery day 0x02 – Friday is a non-delivery day 0x01 – Saturday is a non-delivery day
         */
        dpvNonDeliveryDaysValues?: number;
        /**
         * Flag indicates door is accessible, but package will not be left due to security concerns. Returns a single character.
         */
        dpvNoSecureLocation?: string;
        /**
         * Indicates whether the address is a no stat address or an active address. No stat addresses are ones which are not continuously occupied or addresses that the USPS does not service. Returns a single character.
         */
        dpvNoStat?: string;
        /**
         * Indicates the NoStat type. Returns a reason code as int.
         */
        dpvNoStatReasonCode?: number;
        /**
         * Indicates the address was matched to PBSA record. Returns a single character.
         */
        dpvPBSA?: string;
        /**
         * Indicates that mail is not delivered to the street address. Returns a single character.
         */
        dpvThrowback?: string;
        /**
         * Indicates whether the address is vacant. Returns a single character.
         */
        dpvVacant?: string;
        /**
         * eLOT Ascending/Descending Flag (A/D).
         */
        elotFlag?: string;
        /**
         * Enhanced Line of Travel (eLOT) number.
         */
        elotNumber?: string;
        /**
         * Error message for USPS data retrieval. This is populated when USPS processing is suspended because of the detection of artificially created addresses.
         */
        errorMessage?: string;
        /**
         * FIPS county code.
         */
        fipsCountyCode?: string;
        /**
         * Indicator that a default address was found, but more specific addresses exist.
         */
        hasDefaultAddress: boolean;
        /**
         * The delivery address is matchable, but the EWS file indicates that an exact match will be available soon.
         */
        hasNoEWSMatch: boolean;
        /**
         * LACSLink indicator.
         */
        lacsLinkIndicator?: string;
        /**
         * LACSLink return code.
         */
        lacsLinkReturnCode?: string;
        /**
         * PMB (Private Mail Box) unit designator.
         */
        pmbDesignator?: string;
        /**
         * PMB (Private Mail Box) number.
         */
        pmbNumber?: string;
        /**
         * PO Box only postal code.
         */
        poBoxOnlyPostalCode: boolean;
        /**
         * Main post office city.
         */
        postOfficeCity?: string;
        /**
         * Main post office state.
         */
        postOfficeState?: string;
        /**
         * USPS standardized address.
         */
        standardizedAddress?: USPSAddress;
        /**
         * Footnotes from matching a street or highrise record to suite information. If business name match is found, the secondary number is returned.
         */
        suiteLinkFootnote?: string;
      }

      /**
       * Notice: Available only in the v=beta channel.
       *
       * Represents the post-processed address for the supplied address.
       *
       * Access by calling `const {Verdict} = await google.maps.importLibrary("addressValidation")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      class Verdict {
        /**
         * The address is considered complete if there are no unresolved tokens, no unexpected or missing address components. If unset, indicates that the value is false. See Address.missingComponentTypes, Address.unresolvedTokens or AddressComponent.unexpected fields for more details.
         */
        addressComplete: boolean;
        /**
         * Information about the granularity of the Geocode. This can be understood as the semantic meaning of how coarse or fine the geocoded location is.
         */
        geocodeGranularity?: Granularity;
        /**
         * At least one address component was inferred (i.e. added) that wasn't in the input, see AddressComponent for details.
         */
        hasInferredComponents: boolean;
        /**
         * At least one address component was replaced - see AddressComponent for details.
         */
        hasReplacedComponents?: boolean;
        /**
         * At least one address component cannot be categorized or validated, see AddressComponent for details.
         */
        hasUnconfirmedComponents: boolean;
        /**
         * The granularity of the input address. This is the result of parsing the input address and does not give any validation signals. For validation signals, refer to validationGranularity.
         */
        inputGranularity?: Granularity;
        /**
         * A possible next action to take based on other fields in the API response. See PossibleNextAction for details.
         */
        possibleNextAction?: PossibleNextAction;
        /**
         * The granularity level that the API can fully validate the address to. For example, a validationGranularity of PREMISE indicates all address components at the level of PREMISE and broader can be validated.
         */
        validationGranularity?: Granularity;
      }
    }
    namespace places {
      /**
       * Notice: Available only in the v=beta channel.
       *
       * Prediction results for a Place Autocomplete prediction.
       *
       * Access by calling `const {PlacePrediction} = await google.maps.importLibrary("places")`.
       * See [Libraries in the Maps JavaScript API](https://developers.google.com/maps/documentation/javascript/libraries).
       */
      class PlacePrediction {
        /**
         * The length of the geodesic in meters from origin if origin is specified.
         */
        distanceMeters?: number;
        /**
         * Represents the name of the Place.
         */
        mainText?: FormattableText;
        /**
         * The unique identifier of the suggested Place. This identifier can be used in other APIs that accept Place IDs.
         */
        placeId: string;
        /**
         * Represents additional disambiguating features (such as a city or region) to further identify the Place.
         */
        secondaryText?: FormattableText;
        /**
         * Contains the human-readable name for the returned result. For establishment results, this is usually the business name and address.
         *
         * `text` is recommended for developers who wish to show a single UI element. Developers who wish to show two separate, but related, UI elements may want to use PlacePrediction.mainText and PlacePrediction.secondaryText instead.
         */
        text: FormattableText;
        /**
         * List of types that apply to this Place from Table A or Table B in https://developers.google.com/maps/documentation/places/web-service/place-types.
         */
        types: string[];

        /**
         * Notice: Available only in the v=beta channel.
         *
         * Sends an Address Validation request associated with this autocomplete session (internally populating the request with the autocomplete session token). No place information from the PlacePrediction is included automatically - this is a convenience method to help with autocomplete session management.
         */
        fetchAddressValidation(
          request: google.maps.addressValidation.AddressValidationRequest,
        ): Promise<google.maps.addressValidation.AddressValidation>;

        /**
         * Returns a Place representation of this PlacePrediction. A subsequent call to Place.fetchFields is required to get full Place details.
         *
         * If a AutocompleteRequest.sessionToken was provided in the AutocompleteRequest used to fetch this PlacePrediction, the same token will automatically be included when calling fetchFields.
         *
         * Alternatively, when using PlaceAutocompleteElement the first call to Place.fetchFields on a Place returned by PlacePrediction.toPlace will automatically include the session token.
         */
        toPlace(): Place;
      }
    }
  }
}
